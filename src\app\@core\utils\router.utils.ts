export const ROUTER_UTILS = {
  config: {
    base: {
      root: '/',
      dashboard: 'dashboard',
    },
    auth: {
      root: 'auth',
      login: 'login',
      forgotPassword: {
        root: 'forgot-password',
        init: 'init',
        finish: ':resetKey'
      }
    },
    settings: {
      root: 'settings',
      account: 'account',
      changePassword: 'change-password',
    },
    user: {
      root: 'users',
      profile: ':userId',
    },
    crm: {
      root: 'crm',
      crmContact: {
        root: 'crm-contact',
      },
      crmTask: {
        root: 'crm-task'
      },
      crmCustomer: {
        root: 'crm-customer'
      }
    },
    transport: {
      root: 'transport',
      driverScheduleBoard: {
        root: 'driver-schedule-board'
      },
      incomingTruckBoard: {
        root: 'incoming-truck-board'
      }
    },
    inventory: {
      root: 'inventory',
    },
    pipeline: {
      root: 'pipeline',
      soldTruckBoard: {
        root: 'sold-truck-board',
      },
      stockTruckBoard: {
        root: 'stock-truck-board',
      },
    },
    shops: {
      root: 'shops',
    },
    reporting: {
      root: 'reporting',
      crm: {
        root: 'customer-relationship',
        dailySalesReport: {
          root: 'daily-sales'
        },
        activity: {
          root: 'activity'
        },
        inventorySales: {
          root: 'inventory-sales'
        }
      },
      inventoryReport: {
        root: 'inventory-report',
        profitability: {
          root: 'profitability'
        },
        inventoryAging: {
          root: 'inventory-aging'
        },
      },
      vendorsReport: 'vendors-report',
      supplierReport: 'supplier-report',
    },
    notAuthorized: {
      root: 'not-authorized'
    },
    notFound: {
      root: 'nf'
    },
    deleted: {
      root: 'del'
    },
    administration: {
      root: 'admin',
      vendors: {
        root: 'vendors',
      },
      suppliers: {
        root: 'suppliers',
      },
      pipelineConfig: {
        root: 'pipeline-config',
      },
      users: {
        root: 'users',
      },
      dealers: {
        root: 'dealers',
      },
      shops: 'shops',
      roles: 'roles',
      internetGroupsConfig: 'shadow-groups-config',
      publicPageConfig: {
        root: 'public-page-config',
        advertising: {
          root: 'advertising'
        },
        quoteForm: {
          root: 'quoteForm'
        }
      },
      specificationConfig: {
        root: 'specifications-config',
        category: {
          root: 'category'
        },
        unitType: {
          root: 'unit-type'
        },
        makeModel: {
          root: 'make-model'
        },
        specification: {
          root: 'specification'
        }
      }
    },
    publicInvetory: {
      root: 'public-inventory',
      inventory: 'public-inventories'
    },
    redirectToAuth: {
      root: 'redirect-to-authorized-page'
    },
    calendar: {
      root: 'calendar'
    },
    errorResponse: {
      notFound: '404',
    },
  },
};
