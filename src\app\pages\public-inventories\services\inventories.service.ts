import { Injectable } from '@angular/core';
import { BaseCrudService } from '@core/services';
import { API_URL_UTIL } from '@core/utils';
import { DealerListItem } from '@pages/administration/models';
import { AddInventorySpecificationParams } from '@pages/inventory/models';
import { DocumentListItem } from '@pages/inventory/models/documents.model';
import { NotesDetailsResponse } from '@pages/inventory/models/notes.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class InventoriesService extends BaseCrudService {

  getBaseAPIPath(): string {
    return 'sub-domain';
  }

  getDealerDetails(dealerName: string): Observable<DealerListItem> {
    return this.httpClient.get<DealerListItem>(`sub-domain/${API_URL_UTIL.subDomain.dealer}/${API_URL_UTIL.subDomain.details}/${API_URL_UTIL.subDomain.abbreviation}/${dealerName}`)
  }

  getQuoteFormData(): Observable<any> {
    return this.httpClient.get<any>(`${API_URL_UTIL.subDomain.root}/${API_URL_UTIL.admin.quoteFormConfig.root}`);
  }

  submitQuoteFormData(payload: any): Observable<any> {
    return this.httpClient.post<any>(`${API_URL_UTIL.subDomain.root}/${API_URL_UTIL.admin.quoteFormConfig.root}/send-email`, payload);
  }

  getSpecifications(ids: number[]): Observable<AddInventorySpecificationParams[]> {
    return this.httpClient.post<AddInventorySpecificationParams[]>(`sub-domain/${API_URL_UTIL.inventory.unit}/${API_URL_UTIL.inventory.specification}`, ids)
  }

  getNotes(ids: number[]): Observable<NotesDetailsResponse[]> {
    return this.httpClient.post<NotesDetailsResponse[]>(`sub-domain/${API_URL_UTIL.inventory.root}/${API_URL_UTIL.inventory.notes}`, ids)
  }

  getDocuments(ids: number[]): Observable<DocumentListItem[]> {
    return this.httpClient.post<DocumentListItem[]>(`sub-domain/${API_URL_UTIL.inventory.root}/${API_URL_UTIL.inventory.document}`, ids)
  }

}
