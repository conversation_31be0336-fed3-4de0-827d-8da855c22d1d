import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { AdvertisingModule } from '@pages/administration/pages/public-page-config/pages/advertising/advertising.module';
import { SuppliersModule } from '@pages/administration/pages/suppliers/suppliers.module';
import { VendorsModule } from '@pages/administration/pages/vendors/vendors.module';
import { CrmContactAddModule } from '@pages/crm/pages/crm-contact/crm-contact-add/crm-contact-add.module';
import { CrmContactCustomerAddModule } from '@pages/crm/pages/crm-customer/crm-contact-customer-add/crm-contact-customer-add.module';
import { SoldTruckBoardModule } from '@pages/pipeline/pages/sold-truck-board/sold-truck-board.module';
import { StockTruckBoardModule } from '@pages/pipeline/pages/stock-truck-board/stock-truck-board.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { MentionModule } from 'angular-mentions';
import { AccordionModule } from 'primeng/accordion';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { InputNumberModule } from 'primeng/inputnumber';
import { MenuModule } from 'primeng/menu';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { ProgressBarModule } from 'primeng/progressbar';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TimelineModule } from 'primeng/timeline';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from "../../../../@shared/pipes/pipes.module";
import { ImageZoomOverlayComponent } from "../../../../shared/components/image-zoom-overlay/image-zoom-overlay.component";
import { InternetGroupsConfigModule } from "../../../administration/pages/internet-groups-config/internet-groups-config.module";
import { AddHoldDetailsComponent } from '../inventory-add/add-hold-details/add-hold-details.component';
import { AddNewSaleComponent } from '../inventory-add/add-new-sale/add-new-sale.component';
import { CommunicationComponent } from '../inventory-add/communication/communication.component';
import { EditCommunicationComponent } from '../inventory-add/edit-communication/edit-communication.component';
import { ExpensesAddComponent } from '../inventory-add/expenses-add/expenses-add.component';
import { FinancialWrapperComponent } from '../inventory-add/financial-wrapper/financial-wrapper.component';
import { FinancialComponent } from '../inventory-add/financial/financial.component';
import { InventoryAddComponent } from '../inventory-add/inventory-add.component';
import { InventoryAssociationsComponent } from '../inventory-add/inventory-associations/inventory-associations.component';
import { InventoryConditionComponent } from '../inventory-add/inventory-condition/inventory-condition.component';
import { InventoryDocumentsComponent } from '../inventory-add/inventory-documents/inventory-documents.component';
import { AddNewMakeModule } from '../inventory-add/inventory-general-tab/add-new-make/add-new-make.module';
import { AddNewModelModule } from '../inventory-add/inventory-general-tab/add-new-model/add-new-model.module';
import { AddNewUnitTypeModule } from '../inventory-add/inventory-general-tab/add-new-unit-type/add-new-unit-type.module';
import { InventoryGeneralInfoComponent } from '../inventory-add/inventory-general-tab/inventory-general-info/inventory-general-info.component';
import { InventoryNotesComponent } from '../inventory-add/inventory-notes/inventory-notes.component';
import { InventoryPhotoDetailsComponent } from '../inventory-add/inventory-photo-details/inventory-photo-details.component';
import { InventoryPhotosComponent } from '../inventory-add/inventory-photos/inventory-photos.component';
import { AddSpecificationFieldOptionComponent } from '../inventory-add/inventory-specification/add-specification-field-option/add-specification-field-option.component';
import { InventorySpecificationComponent } from '../inventory-add/inventory-specification/inventory-specification.component';
import { InventoryDocumentDetailsComponent } from '../inventory-document-details/inventory-document-details.component';
import { InventoryHoldModule } from '../inventory-hold/inventory-hold.module';
import { InventorySearchComponent } from '../inventory-search/inventory-search.component';
import { InventorySoldModule } from '../inventory-sold/inventory-sold.module';
import { InventoryAddWrapperComponent } from './inventory-add-wrapper.component';

@NgModule({
  declarations: [
    InventoryAddWrapperComponent,
    InventoryAddComponent,
    InventorySearchComponent,
    InventoryPhotosComponent,
    InventoryConditionComponent,
    InventoryNotesComponent,
    InventoryDocumentsComponent,
    FinancialComponent,
    ExpensesAddComponent,
    InventoryDocumentDetailsComponent,
    AddNewSaleComponent,
    AddHoldDetailsComponent,
    InventoryPhotoDetailsComponent,
    CommunicationComponent,
    InventoryGeneralInfoComponent,
    EditCommunicationComponent,
    InventorySpecificationComponent,
    AddSpecificationFieldOptionComponent,
    InventoryAssociationsComponent,
    FinancialWrapperComponent
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    FormsModule,
    TabViewModule,
    ConfirmPopupModule,
    DropdownModule,
    CardModule,
    TableModule,
    RadioButtonModule,
    ButtonModule,
    DialogModule,
    DividerModule,
    CheckboxModule,
    ProgressBarModule,
    CheckboxModule,
    PanelModule,
    AccordionModule,
    MessageModule,
    EditorModule,
    CarouselModule,
    MultiSelectModule,
    TooltipModule,
    TimelineModule,
    CalendarModule,
    AvatarModule,
    StockTruckBoardModule,
    SoldTruckBoardModule,
    CrmContactAddModule,
    VendorsModule,
    AddNewModelModule,
    AddNewMakeModule,
    GoogleMapModule,
    // GooglePlaceModule,
    NgxGpAutocompleteModule,
    MentionModule,
    InputNumberModule,
    DragDropModule,
    CrmContactCustomerAddModule,
    AddNewUnitTypeModule,
    InventorySoldModule,
    InventoryHoldModule,
    SuppliersModule,
    AdvertisingModule,
    MenuModule,
    ConfirmDialogModule,
    PipesModule,
    InternetGroupsConfigModule,
    ImageZoomOverlayComponent
],
  exports: [
    InventoryAddWrapperComponent,
    InventoryAssociationsComponent
  ],
  providers: [TitleCasePipe, {
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }]
})

export class InventoryAddWrapperModule { }
