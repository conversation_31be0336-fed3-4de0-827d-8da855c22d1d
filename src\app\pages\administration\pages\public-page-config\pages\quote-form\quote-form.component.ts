import { Component, OnInit } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { InventoriesService } from '@pages/public-inventories/services';
import { User } from '@pages/user/models';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { Field, QuoteFormData } from '../../models/quoteForm.model';
import { QuoteFormService } from './quote-form.service';

@Component({
  selector: 'app-quote-form',
  templateUrl: './quote-form.component.html',
  styleUrls: ['./quote-form.component.scss']
})
export class QuoteFormComponent extends BaseComponent implements OnInit {

  selectedField!: Field | null;
  openModal = false;
  isEditMode = false;
  isUpdateEmail = false;
  quoteFormId!: number;
  fields: Field[] = [];
  userIds: number[] = [];
  users: User[] = [];
  selectedUsers!: User[];
  quoteFormData!: QuoteFormData

  constructor(private readonly quoteFormService: QuoteFormService, private readonly commonSharedService: CommonSharedService, private readonly confirmationService: ConfirmationService, private readonly toasterService: AppToasterService, private readonly userAnnotationService: UserAnnotationService, private readonly inventoriesService: InventoriesService) {
    super();
    this.pageTitle = 'Quote Form Config'
  }

  ngOnInit(): void {
    this.getUsers();
  }

  getQuoteConfigFields(): void {
    this.isLoading = true;
    this.inventoriesService.getQuoteFormData().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.quoteFormData = res[0];
      this.quoteFormId = res[0].id;
      this.fields = res[0].formData.fields;
      this.userIds = res[0].userIds;
      this.selectedUsers = this.users.filter((user: User) => this.userIds.includes(user.id));
      this.isLoading = false;
    })
  }

  getUsers(): void {
    this.isLoading = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res;
          this.getQuoteConfigFields();
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  getQuoteFormParams(): QuoteFormData {
    return {
      id: this.quoteFormId,
      formData: {
        fields: this.fields
      },
      userIds: this.userIds
    }
  }

  onAddEditField(field?: Field): void {
    if (field) {
      this.selectedField = field;
      this.isEditMode = true;
    } else {
      this.selectedField = null;
      this.isEditMode = false;
    }
    this.openModal = true;
  }

  onUpdateEmailList(): void {
    this.isUpdateEmail = true;
    this.openModal = true;
  }

  onDelete(field: Field): void {
    if (field?.isDefault) {
      this.toasterService.warning(MESSAGES.disableDeleteField);
      return;
    }

    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteQuoteFieldWarning.replace('{record}', field.label),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(field);
      }
    });
  }

  private onDeleteConfirmation(field: Field): void {
    this.fields = this.fields.filter((f) => f.label !== field.label);
    this.updateQuoteForm();
    this.toasterService.success(MESSAGES.deleteQuoteFieldSuccess.replace('{record}', field.label));
  }

  onRowReorder(): void {
    this.updateQuoteForm();
  }

  updateQuoteForm(): void {
    this.commonSharedService.setBlockUI$(true);
    this.quoteFormService.update(this.getQuoteFormParams()).subscribe((res: QuoteFormData) => {
      this.quoteFormId = res.id;
      this.fields = res.formData.fields;
      this.userIds = res.userIds;
      this.commonSharedService.setBlockUI$(false);
    })
  }

  closeFieldModal(dataHasBeenModified: boolean): void {
    this.openModal = false;
    this.isUpdateEmail = false;
    this.isEditMode = false;
    dataHasBeenModified && this.getQuoteConfigFields();
  }

}
