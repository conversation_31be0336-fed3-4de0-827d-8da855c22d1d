<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <span *ngIf="isEditMode" class="created-by">
    <span class="bold-text">#{{ dealerInfo?.name }}</span>
    <ng-container *ngIf="dealerInfo?.createdBy; else noCreatedBy">
      Created By <span class="bold-text">{{ dealerInfo?.createdBy }}</span> on
      {{ dealerInfo?.createdDate | date : constants.fullDateFormat }}
    </ng-container>
    <ng-template #noCreatedBy> Created on {{ dealerInfo?.createdDate | date : constants.fullDateFormat }} </ng-template>
  </span>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form [formGroup]="dealerFormGroup" class="dealer-form" (ngSubmit)="onSubmit()">
  <div class="content">
    <section class="card p-3 pb-4">
      <div class="title dealer-title">
        <h4>Dealer Details</h4>
        <div class="public-url" *ngIf="dealerInfo?.id">
          Public Url:
          <a target="_blank" [href]="environment.forntendUrl + 'public-inventory/' + dealerInfo?.abbreviation">
            {{ environment.forntendUrl + 'public-inventory/' + dealerInfo?.abbreviation }}
          </a>
        </div>
      </div>
      <div class="d-flex dealer-details-container">
        <div class="dealer-logo">
          <div class="d-flex justify-content-between align-items-center document-upload">
            <label class="m-t-20">Logo</label>
          </div>
          <!-- Upload drop zone - only show when no logo exists -->
          <div *ngIf="!fileUploadProgresses.length && !dealerInfo?.fullLogoUrl" class="m-t-10">
            <section class="w-100 mb-0">
              <div class="drop-zone">
                <span class="drop-zone__prompt">
                  <em class="pi pi-upload"></em>
                  <p class="title text-nowrap">Upload Logo</p>
                  <p class="subtitle text-nowrap">click to browse</p>
                </span>
                <input type="file" name="myFile" class="drop-zone__input cursor-pointer" #inputElement
                  [accept]="constants.allowedImgFormats" (change)="onFileSelect($event)" />
              </div>
            </section>
          </div>

          <!-- Show uploaded logo image instead of drop zone -->
          <div *ngIf="fileUploadProgresses.length || dealerInfo?.fullLogoUrl" class="uploaded-logo-display m-t-10">
            <!-- Newly uploaded logo -->
            <div *ngIf="fileUploadProgresses.length" class="logo-preview-container">
              <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index" class="logo-preview">
                <img [src]="fileProgress.fileProperty?.fileUrl" [alt]="'Dealer Logo'" class="dealer-logo-image"
                  (click)="onViewImage(fileProgress?.fileProperty?.fileUrl)" />
                <div class="logo-overlay" *ngIf="!fileProgress.isResolved">
                  <p-progressBar [value]="fileProgress?.progress$ | async" [showValue]="true"></p-progressBar>
                </div>
                <div class="logo-actions">
                  <em class="action-icon" (click)="inputElement.click()" title="Change Logo">
                    <img src="assets/images/icons/change-image.png" alt="Change Logo"
                      style="width: 24px; height: 24px;" />
                  </em>
                  <em class="pi pi-trash action-icon delete-icon" (click)="removeFileFromUpload(fileIndex)"
                    title="Delete"></em>
                  <input type="file" name="myFile" class="d-none" #inputElement [accept]="constants.allowedImgFormats"
                    (change)="onFileSelect($event)" />
                </div>
              </div>
            </div>

            <!-- Existing logo in edit mode -->
            <div *ngIf="dealerInfo?.fullLogoUrl && !fileUploadProgresses.length" class="logo-preview-container">
              <div class="logo-preview">
                <img [src]="dealerInfo?.fullLogoUrl" [alt]="dealerInfo?.name + ' logo'" class="dealer-logo-image"
                  (click)="onViewImage(dealerInfo?.fullLogoUrl)" />
                <div class="logo-actions">
                  <em class="action-icon" (click)="inputElement.click()" title="Change Logo">
                    <img src="assets/images/icons/change-image.png" alt="Change Logo"
                      style="width: 24px; height: 24px;" />
                  </em>
                  <em class="pi pi-download action-icon" (click)="downloadImage()" title="Download"></em>
                  <em *ngIf="!isFormDisabled" class="pi pi-trash action-icon delete-icon" (click)="deleteExistingLogo()"
                    title="Delete"></em>
                  <input type="file" name="myFile" class="d-none" #inputElement [accept]="constants.allowedImgFormats"
                    (change)="onFileSelect($event)" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row w-100 ms-md-3 dealer-details">
          <div class="col-lg-6 col-md-6 col-12">
            <label class="required">Dealer</label>
            <input class="form-control" type="text" placeholder="Enter dealer name" formControlName="name" />
            <app-error-messages [control]="dealerFormGroup.controls.name"></app-error-messages>
          </div>
          <div class="col-lg-6 col-md-6 col-12">
            <label>Email</label>
            <input class="form-control" type="text" placeholder="Enter dealer email" formControlName="email" />
            <app-error-messages [control]="dealerFormGroup.controls.email"></app-error-messages>
          </div>
          <div class="col-lg-6 col-md-6 col-12">
            <label class="required">Phone number</label>
            <input class="form-control" type="text" [mask]="contactNoFormat" placeholder="Enter phone number"
              formControlName="phoneNumber" />
            <app-error-messages [control]="dealerFormGroup.controls.phoneNumber"></app-error-messages>
            <span class="text-danger f-s-12"
              *ngIf="dealerFormGroup.controls.phoneNumber?.touched && dealerFormGroup.controls.phoneNumber.errors?.mask">
              Please enter valid phone number
            </span>
          </div>
          <div class="col-lg-6 col-md-6 col-12">
            <label class="required">Abbreviation</label>
            <input class="form-control" type="text" pKeyFilter="alphanum" placeholder="Enter abbreviation"
              formControlName="abbreviation" />
            <app-error-messages [control]="dealerFormGroup.controls.abbreviation"></app-error-messages>
          </div>
        </div>
      </div>
    </section>
    <section formGroupName="contactPerson" class="card p-3 pb-4">
      <div class="title">
        <h4>Contact Person Details</h4>
      </div>
      <div class="row">
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">First Name</label>
          <input class="form-control" type="text" placeholder="Enter contact person first name" formControlName="firstName" />
          <app-error-messages [control]="contactPersonFormGroup.controls.firstName"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Last Name</label>
          <input class="form-control" type="text" placeholder="Enter contact person last name" formControlName="lastName" />
          <app-error-messages [control]="contactPersonFormGroup.controls.lastName"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label>Email</label>
          <input class="form-control" type="email" placeholder="Enter contact person email" formControlName="email" />
          <app-error-messages [control]="contactPersonFormGroup.controls.email"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
          <label class="required">Phone number</label>
          <input class="form-control" type="text" [mask]="contactNoFormat" placeholder="Enter phone number" formControlName="phoneNumber" />
          <app-error-messages [control]="contactPersonFormGroup.controls.phoneNumber"></app-error-messages>
          <span class="text-danger f-s-12" *ngIf="contactPersonFormGroup.controls.phoneNumber?.touched && contactPersonFormGroup.controls.phoneNumber.errors?.mask">
            Please enter valid phone number
          </span>
        </div>
      </div>
    </section>
    <section formGroupName="address" class="card p-3 pb-4">
      <div class="title">
        <h4>Address Details</h4>
      </div>
      <div class="row">
        <div class="col-md-6 col-12">
          <label class="required">Address</label>
          <input
            class="form-control"
            type="text"
            ngx-gp-autocomplete
            [options]="options"
            (onAddressChange)="handleAddressChange($event)"
            placeholder="Enter address"
            formControlName="streetAddress"
          />
          <app-error-messages [control]="addressFormGroup.controls.streetAddress"></app-error-messages>
        </div>
        <div class="col-lg-2 col-md-6 col-12">
          <label class="required">City</label>
          <input class="form-control" type="text" placeholder="Enter city name" formControlName="city" />
          <app-error-messages [control]="addressFormGroup.controls.city"></app-error-messages>
        </div>
        <div class="col-lg-2 col-md-6 col-12">
          <label class="required">State</label>
          <input class="form-control" type="text" placeholder="Enter state name" formControlName="state" />
          <app-error-messages [control]="addressFormGroup.controls.state"></app-error-messages>
        </div>
        <div class="col-lg-1 col-md-5 col-10">
          <label class="required">Zip code</label>
          <input class="form-control" type="text" placeholder="Enter zip code" formControlName="zipcode" />
          <app-error-messages [control]="addressFormGroup.controls.zipcode"></app-error-messages>
        </div>
        <div class="col-1">
          <div
            class="map-icon"
            *ngIf="
              addressFormGroup.get('streetAddress')?.value || addressFormGroup.get('city')?.value || addressFormGroup.get('state')?.value || addressFormGroup.get('zipcode')?.value
            "
          >
            <button class="btn btn-primary" type="button" (click)="toggleGoogleMapPopUp()">
              <fa-icon [icon]="faIcons.faLocationDot"></fa-icon>
            </button>
          </div>
        </div>
      </div>

      <p-sidebar
        [closeOnEscape]="false"
        [dismissible]="false"
        [(visible)]="showGoogleMapSideBar"
        position="right"
        (onHide)="showGoogleMapSideBar = false"
        [blockScroll]="true"
        [showCloseIcon]="false"
        styleClass="p-sidebar-md"
        [baseZIndex]="10000"
        appendTo="body"
      >
        <app-google-map (onClose)="toggleGoogleMapPopUp()" *ngIf="showGoogleMapSideBar" [addressGroup]="addressFormGroup.value" [address]="fullAddress"></app-google-map>
      </p-sidebar>
    </section>
    <section class="card p-3 pb-4">
      <div class="title">
        <h4>Funding Email</h4>
      </div>
      <div class="d-flex align-items-center">
        <p-timeline [value]="emailFormArray.controls | keyvalue">
          <ng-template pTemplate="marker" let-event>
            <span>{{ getSequenceNumber(event.key) }}</span>
          </ng-template>
        </p-timeline>
        <p-table [value]="emailFormArray.controls" responsiveLayout="scroll" class="draggable">
          <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
            <ng-container formArrayName="fundingEmails">
              <tr [formGroupName]="rowIndex">
                <td class="email-col">
                  <input class="form-control" type="email" placeholder="Enter email" formControlName="email" />
                  <app-error-messages [control]="getEmailFormGroup(rowIndex).controls.email"></app-error-messages>
                </td>
                <td>
                  <img [src]="constants.staticImages.icons.deleteIcon" alt="delete" (click)="onDeleteShop(rowIndex)" />
                </td>
              </tr>
            </ng-container>
          </ng-template>
        </p-table>
      </div>
      <div class="add-shop mt-4">
        <button
          class="btn btn-primary"
          id="addEmailBtn"
          type="button"
          [disabled]="isFormDisabled"
          *ngIf="emailFormArray.length < 3"
          [appImageIconSrc]="constants.staticImages.icons.addNew"
          (click)="onAddNewShop()"
        >
          Add Email
        </button>
      </div>
    </section>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="!isFormDisabled && !isArchived">Save</button>
    <button class="btn btn-primary" type="button" (click)="onSubmitAndAddNew()" *ngIf="!isEditMode && !isArchived" appShowLoaderOnApiCall>Save & Add New</button>
  </div>
</form>

<app-image-zoom-overlay
  [images]="zoomImages"
  [selectedIndex]="selectedPhotoIndex"
  [isVisible]="isShowCarousel"
  (close)="closeZoomOverlay()"
  (indexChange)="onImageIndexChange($event)">
</app-image-zoom-overlay>
