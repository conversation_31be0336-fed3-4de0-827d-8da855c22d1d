<app-page-header [pageTitle]="pageTitle">
  <div class="top-header" headerActionBtn>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="exportUsersToExcel()"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <button type="button" class="btn btn-primary btn-sm" (click)="clearSearchInput()">Reset filters</button>
  </div>
</app-page-header>
<div class="card tabs stock-truck-list">
  <div class="tab-content">
    <p-table
      [columns]="selectedColumns"
      styleClass="p-datatable-gridlines"
      [value]="inventorySalesList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [resizableColumns]="true"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn pReorderableColumn [pSortableColumn]="col?.shortingKey" scope="col">{{ col.name }} <p-sortIcon [field]="col?.shortingKey"></p-sortIcon></th>
          </ng-container>
        </tr>
        <tr class="report-search-tr">
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn scope="col">
              <span class="search-input" *ngIf="col.type === 'STRING'">
                <input pInputText placeholder="Search {{ col.name }}" type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DOUBLE'">
                <input pInputText placeholder="Search {{ col.name }}" type="number" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN' && col.searchKey === 'salesPerson.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="salesRepList"
                  defaultLabel="Select a {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="accountRepIds"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.salesRep, data: salesRepList }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="col.value?.length" (click)="clearSalesRep(col)"></fa-icon>
              </span>
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN' && col.searchKey === 'unit.generalInformation.unitTypeCategory.id'">
                <p-dropdown
                  appPreventClearFilter
                  [options]="categoryTypes"
                  [(ngModel)]="selectedCategoryId"
                  (onFocus)="getCategoryTypes()"
                  (onChange)="tableSearchByColumn($event, col)"
                  optionLabel="name"
                  optionValue="id"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="selectedCategoryId ? true : false"
                  appendTo="body"
                  placeholder="Select Category"
                  (onClear)="onCategoryClear()"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.category, data: categoryTypes }"></ng-container>
                  </ng-template>
                </p-dropdown>
              </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN' && col.searchKey === 'unit.generalInformation.unitType.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="unitTypes"
                  defaultLabel="Select a {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getUnitType()"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="unitTypeIds"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.unitType, data: unitTypes }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="unitTypeIds?.length" (click)="clearUnitTypes(col)"></fa-icon>
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'">
                <p-calendar appendTo="body" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" placeholder="mm/dd/yyyy" inputId="startDateIcon" (onSelect)="tableSearchByColumn($event, col)" (onClearClick)="clearDate()" [(ngModel)]="invoiceDate"></p-calendar>
              </span>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-saleData let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td>
              <span *ngIf="col.type !== 'DATE' && col.type !== 'DOUBLE'">
                {{ getEvaluatedExpression(col.key, saleData) }}
              </span>
              <span *ngIf="col.type === 'DATE'">
                {{ getEvaluatedExpression(col.key, saleData) | date: constants.dateFormat }}
              </span>
              <span *ngIf="col.type === 'DOUBLE'">
                {{ getEvaluatedExpression(col.key, saleData) | currency: 'USD' : 'symbol': '1.0-2' }}
              </span>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="dropDownColumnList.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>
