import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, FormGroupDirective } from '@angular/forms';
import { BaseComponent } from '@core/utils';
import { IncomingTruckStatusList } from '@pages/transport/models/incoming-truck.model';
import { Address } from 'ngx-google-places-autocomplete/objects/address';

@Component({
  selector: 'app-pickup-info',
  templateUrl: './pickup-info.component.html',
  styleUrls: ['./pickup-info.component.scss']
})
export class PickupInfoComponent extends BaseComponent implements OnInit {
  driverScheduleAddress!: FormGroup;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  showGoogleMapSideBar = false;
  fullAddress!: string;
  inventoryStatuses = IncomingTruckStatusList;
  @Input() driverScheduleAddressForm!: string;
  @Input() isViewMode !: boolean | null;
  @Input() status!: string | undefined;
  @Input() pickupDate!: Date | undefined;
  @Input() isStatusError !: boolean;
  @Input() pickupNotes!: string;

  @Output() statusChanged = new EventEmitter<string>();
  @Output() pickupNotesChanged = new EventEmitter<string>();
  @Output() pickupDateChanged = new EventEmitter<Date>();

  constructor(
    private readonly fb: FormBuilder,
    private readonly rootFormGroup: FormGroupDirective
  ) { super(); }

  ngOnInit(): void {
    this.driverScheduleAddress = this.rootFormGroup.control.get(this.driverScheduleAddressForm) as FormGroup;
  }

  handleAddressChange(address: Address | any) {
    if (address?.address_components) {
      this.driverScheduleAddress.controls['streetAddress'].setValue(address.name);
      this.driverScheduleAddress.controls['latitude'].setValue(address.geometry.location.lat());
      this.driverScheduleAddress.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':

            break;
          case 'locality': // city
            this.driverScheduleAddress.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1': // state
            this.driverScheduleAddress.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.driverScheduleAddress.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress()
  }

  onStatusChange() {
    this.isStatusError = !(!!this.status);
    this.statusChanged.emit(this.status);
  }

  onPickupNotesChange() {
    this.pickupNotesChanged.emit(this.pickupNotes);
  }

  onPickupDateChange() {
    this.pickupDateChanged.emit(this.pickupDate);
  }

  getFullAddress() {
    const rowAddress = []
    const address = this.driverScheduleAddress.value;
    rowAddress.push(address?.streetAddress ? address.streetAddress : '')
    rowAddress.push(address?.city ? address.city : '')
    rowAddress.push(address?.state ? address.state : '')
    rowAddress.push(address?.zipcode ? address.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }
}
