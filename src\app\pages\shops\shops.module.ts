import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { MentionModule } from 'angular-mentions';
import { UiSwitchModule } from 'ngx-ui-switch';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { ProgressBarModule } from 'primeng/progressbar';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { ImageZoomOverlayComponent } from 'src/app/shared/components/image-zoom-overlay/image-zoom-overlay.component';
import { TaskAddComponent } from './pages/task-add/task-add.component';
import { TaskAuditComponent } from './pages/task-audit/task-audit.component';
import { TaskCommentsComponent } from './pages/task-comments/task-comments.component';
import { TaskListComponent } from './pages/task-list/task-list.component';
import { ShopsRoutingModule } from './shops-routing.module';
import { ShopsComponent } from './shops.component';

@NgModule({
  declarations: [
    ShopsComponent,
    TaskListComponent,
    TaskAddComponent,
    TaskCommentsComponent,
    TaskAuditComponent,
  ],
  imports: [
    CommonModule,
    ShopsRoutingModule,
    AccordionModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    ConfirmDialogModule,
    ProgressBarModule,
    CalendarModule,
    TabViewModule,
    TooltipModule,
    CheckboxModule,
    DividerModule,
    MessageModule,
    EditorModule,
    MentionModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    DialogModule,
    ImageZoomOverlayComponent
  ],
  providers: [
    ConfirmationService
  ],
  exports: [TaskAddComponent, TaskAuditComponent, TaskCommentsComponent]
})
export class ShopsModule { }
