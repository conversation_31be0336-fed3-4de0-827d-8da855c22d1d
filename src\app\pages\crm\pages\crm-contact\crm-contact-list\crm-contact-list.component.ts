import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HistoryModuleName, MESSAGES, dateFormat, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent, ROUTER_UTILS } from '@core/utils';
import { CrmContactListFilter, CrmContactListItem } from '@pages/administration/models/crm.model';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, OperatorType, RoleNames, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-crm-contact-list',
  templateUrl: './crm-contact-list.component.html',
  styleUrls: ['./crm-contact-list.component.scss']
})
export class CrmContactListComponent extends BaseComponent implements OnInit {
  crmContactList: CrmContactListItem[] = [];
  selectedContacts: CrmContactListItem[] = [];
  filterParams: any = new CrmContactListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  showChangeAccountRepModal = false;
  historyModulesName = [HistoryModuleName.CRM_CONTACT, HistoryModuleName.REMINDERS];
  showConfirmationDialog = false;
  showConfirmationPopup = true;
  selectedCrmContactList!: CrmContactListItem | null;
  isCrmContactViewMode = false;
  selectAll = false;
  cols: any[] = [];
  _selectedColumns: any[] = [];
  showColumnModal = false;
  defaultTabs!: FilterList[];
  tableColumn!: FilterList | undefined;
  defaultColumnsArray: ColumnItem[] = [];
  dropDownColumnList: ColumnItem[] = [];
  filterValue: FilterValue[] = [];
  globalSearch = new Subject<FilterValue[]>();
  hideFieldForSearch: string | null = '';
  initialFilterParams: any;
  isArchiveInProgress = false;
  activeTabDetail: any;
  userPermissions!: Array<PrivilegeActionResponseDTOs>
  selectAllDisabled: boolean = false;
  activeIndex = 1;
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columns",
      command: () => this.exportUsersToExcel(true)
    }
  ];

  constructor(
    private readonly crmService: CrmService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly columnDropDownService: ColumnDropdownService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly activeRoute: ActivatedRoute,
    private readonly datePipe: DatePipe,
    private readonly router: Router
  ) {
    super();
    this.pageTitle = 'Contact Management';
    this.isRowSelectable = this.isRowSelectable.bind(this);
  }

  async ngOnInit() {
    await this.getCurrentUser();
    this.getFilterDetail();
    this.getFilterSaveParams();
    this.displaySearchResult();
    this.filterParams = this.contactInfoParams;
    this.initialFilterParams = Object.assign([], this.filterParams.values)
    this.router.navigate([], { queryParams: {} });
    this.userPermissions = this.authService.getRoleInfo().privilegeActionResponseDTOs;
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.contactId) {
          this.getContactDetails(Number(params.contactId));
        }
      });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  getContactDetails(id: number): void {
    const endpoint = API_URL_UTIL.admin.crm.contact;
    this.crmService.get<CrmContactListItem>(id, endpoint).pipe(takeUntil(this.destroy$)).subscribe((crmContactDetails) => {
      if (crmContactDetails.id === null) {
        this.router.navigate([ROUTER_UTILS.config.notFound.root, ROUTER_UTILS.config.crm.crmContact.root, ROUTER_UTILS.config.notFound.root]);
      }
      else if (crmContactDetails.id && crmContactDetails.deleted === true) {
        this.router.navigate([ROUTER_UTILS.config.notFound.root, ROUTER_UTILS.config.crm.crmContact.root, ROUTER_UTILS.config.deleted.root]);
        return;
      } else {
        this.onViewEdit(crmContactDetails, false);
      }
      this.cdf.detectChanges();
    });
  }

  onTabChanged(tabChangeEvent: any): void {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.selectedContacts = [];
    if (this.defaultTabs[tabChangeEvent.index]?.data) {
      const tabData = JSON.parse(this.defaultTabs[tabChangeEvent.index].data as any);
      this.filterParams = tabData;
      this.initialFilterParams = Object.assign([], this.filterParams.values);
      this.hideFieldForSearch = this.defaultTabs[tabChangeEvent.index]?.hideField ?? null
      this.activeTabDetail = this.defaultTabs[tabChangeEvent.index];
    }
    this.getAll();
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }
  get contactInfoParams() {
    return {
      archived: false,
      treeOperator: TreeOperatorType.AND,
      values: [
        {
          dataType: DataType.BOOLEAN,
          key: 'isProspect',
          operator: OperatorType.EQUAL,
          value: false
        },
        {
          dataType: DataType.BOOLEAN,
          key: 'unAssigned',
          operator: OperatorType.EQUAL,
          value: false
        },
      ]
    };
  }
  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.crmService.getListWithFiltersWithPagination<CrmContactListFilter, CrmContactListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.crm.filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.crmContactList = res.content.map(contact => ({
            ...contact,
            isActive: !contact.archived
          }));
          this.setPaginationParamsFromPageResponse<CrmContactListItem>(res);
          this.selectedContacts = this.selectedContacts.filter(contact => res.content.some(resContact => resContact.id === contact.id));
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.showConfirmationPopup = false;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.showConfirmationPopup = true;
    this.selectedCrmContactList = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onEdit(crmContact: CrmContactListItem): void {
    this.showCreateModal = true;
    this.selectedCrmContactList = crmContact;
    this.cdf.detectChanges();
  }

  onDelete(pipelineConfig: CrmContactListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteContactWarning.replace('{record}', 'crm contact data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(pipelineConfig);
      }
    });
  }

  onDeleteConfirmation(task: CrmContactListItem): void {
    const endpoint = API_URL_UTIL.admin.crm.contact;
    this.crmService.delete(task.id, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.customerContactDeleted);
          this.getAll();
        }
      });
  }

  onViewEdit(crm: CrmContactListItem, isEdit: boolean): void {
    this.showConfirmationPopup = false;
    this.isCrmContactViewMode = !isEdit;
    this.selectedCrmContactList = crm;
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  sortColumnList(columns: ColumnItem[]) {
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'Prospect');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'Prospect' && x.name !== 'Action');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return tempData.concat(tempData1, tempData2);
  }

  sortSelectedColumns() {
    let temp: any[] = [];
    const ids = this.defaultColumnsArray.map(a => a.id);
    this.defaultColumnsArray = this.sortColumnList(this.defaultColumnsArray)
    this._selectedColumns = this.cols = this.defaultColumnsArray;

    if (this.tableColumn && this.tableColumn?.data) {
      temp = JSON.parse(this.tableColumn?.data);
    }

    this.dropDownColumnList.forEach((column: any) => {
      if (!ids.includes(column.id)) {
        temp.push(column);
      }
    });

    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
  }

  getFilterSaveParams(): FilterList {
    return {
      module: FilterModuleName.CRM_CONTACT_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }

  getFilterDetail(): void {
    this.defaultColumnsArray = [];
    const userId = this.currentUser?.id ?? null;
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${FilterModuleName.CRM_CONTACT_MODULE}`)
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (filters) => {
        this.defaultTabs = filters.filter((item: any) => item.filterType === "TAB");
        this.tableColumn = filters.find((item: any) => item.filterType === "COLUMN");
        if (this.tableColumn?.data) {
          this.defaultColumnsArray = JSON.parse(this.tableColumn?.data);
          this.getDropDownColumnList();
          this.cdf.detectChanges();
        } else {
          this.getDropDownColumnList(true);
        }
      }
    });
  }

  getDropDownColumnList(shouldCreateDefaultColumn = false) {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${FilterModuleName.CRM_CONTACT_MODULE.toUpperCase()}`);
    this.commonService.getListFromObject<ColumnItem>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.sortSelectedColumns();
        }
        this.cdf.detectChanges();
      });
  }

  callFilterApiAgain() {
    this.getFilterDetail();
  }

  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: FilterModuleName.CRM_CONTACT_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }

  createDefaultColumns(): void {
    const defaultFields = this.dropDownColumnList.filter(a => a.default ? a.name : null)
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.defaultColumnsArray = JSON.parse(res.data);
            this.sortSelectedColumns()
            this.cdf.detectChanges();
          }
        }
      });
    }
  }

  assignDataType(type: string): string {
    let stringDataType = '';
    switch (type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DROP_DOWN':
        stringDataType = DataType.ENUM
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }

  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DOUBLE':
        operatorType = OperatorType.EQUAL
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }

  allowActions(data: CrmContactListItem): boolean {
    if (!data?.accountReporter?.id) {
      return true;
    }
    if (this.currentUser?.role.name.toLowerCase() === RoleNames.ROLE_SALESPERSON && this.currentUser?.id !== data?.accountReporter?.id) {
      return false;
    }
    return true;
  }

  isRowSelectable(event: { data: CrmContactListItem; index: number }) {
    return this.allowActions(event.data);
  }

  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = event.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.shortingKey)
    if (!existingValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col.type) as DataType,
        key: col.shortingKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (inputValue?.length) {
        existingValue.value = inputValue;
      } else {
        this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
      }
    }
    if (col.type === 'DROP_DOWN' && inputValue === 'ALL') {
      const rm = this.filterParams.values.find((d: any) => d.key === 'status')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }
    }
  }

  tableSearchByColumn(event: any, col: any) {
    this.isLoading = true;
    this.crmContactList = [];
    const searchInput = this.getEventValue(event, col)
    this.getFilterInfo(searchInput, col)
    this.setSearchEndDate(event, col);
    this.globalSearch.next(this.filterParams.values);
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    temp.value = (col.type === 'DATE') ? this.datePipe.transform(searchInput, 'MM/dd/yyyy') : searchInput;
    if (temp1) {
      temp1.value = temp.value;
    }
  }

  setSearchEndDate(event: any, col: any) {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.shortingKey && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col.type) as DataType,
        key: col.shortingKey,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
      this.getAll();
    });
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  getCrmContactsCount() {
    return this.crmContactList.length;
  }

  getSelectedContactsCount() {
    return this.selectedContacts.length;
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.filterParams.values = Object.assign([], this.initialFilterParams);
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  onProspectToCustomerChange(crmDetails: CrmContactListItem, isActive: boolean): void {
    this.showConfirmationDialog = true;
    this.cdf.detectChanges();
    this.selectedCrmContactList = crmDetails;
    this.isArchiveInProgress = true;
    this.confirmationService.confirm({
      message: 'Are you sure you want to convert this prospect to customer?',
      header: 'Confirmation',
      icon: icons.triangle,
      accept: () => {
        this.isArchiveInProgress = true;
        crmDetails.prospect = isActive;
        this.showConfirmationDialog = false;
        this.onProspectToCustomerChangeConfirmation(crmDetails);
      },
      reject: () => {
        crmDetails.prospect = !isActive;
        this.isArchiveInProgress = false;
        this.showConfirmationDialog = false;
        this.cdf.detectChanges();
      }
    });
  }

  private onProspectToCustomerChangeConfirmation(inventory: CrmContactListItem): void {
    this.crmService.patch(undefined, `contacts/${inventory.id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.prospectToCustomerSuccess);
          this.isArchiveInProgress = false;
          this.selectedCrmContactList = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedCrmContactList = null;
          this.getAll();
        }
      });
  }

  unAssignReporter(crmDetail: any, event: Event) {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.unassignReporterConfirmation,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onUnAssignConfirmation(crmDetail);
      }
    });
  }

  onUnAssignConfirmation(crmDetail: any) {
    const endpoint = `${API_URL_UTIL.admin.crm.contact}/${API_URL_UTIL.admin.crm.reporter}`;
    const requestParams = {
      ids: [crmDetail?.id],
      accountRepId: null
    }
    this.crmService.patch(requestParams, endpoint).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.toasterService.success(MESSAGES.unassignReporterMessage);
          this.getAll();
        }
      });

  }

  unAssignReporterForSelectedContact(event: Event) {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.unassignReporterForSelectedContactsConfirmation,
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onUnassignSelectContacts();
      }
    });
  }

  onUnassignSelectContacts() {
    const endpoint = `${API_URL_UTIL.admin.crm.contact}/${API_URL_UTIL.admin.crm.reporter}`;
    const requestParams = {
      ids: this.selectedContacts.map(contact => contact.id),
      accountRepId: null
    }
    this.crmService.patch(requestParams, endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.unAssignAccountRepSuccess);
      this.getAll();
    });
  }


  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    this.crmService.getListWithFiltersWithPagination<CrmContactListFilter, CrmContactListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.admin.crm.filter)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content, downloadAll)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "crm-contacts");
          this.isExporting = false;
        });
      });
  }

  getExcelData(crmContact: Array<CrmContactListItem>, downloadAll = false) {
    const excelData = crmContact.map(res => ({
      'Prospect': res?.prospect,
      'First Name': res?.firstName,
      'Last Name': res?.lastName,
      'Company': res?.company,
      'Work': res?.primaryPhone,
      'Mobile': res?.secondaryPhone,
      'Email': res?.primaryEmail,
      'City': res?.city,
      'State': res?.state,
      'Account Rep': res?.accountReporter?.name
    }))
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.defaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }
    return excelData;
  }

  onBackdropClick(element: HTMLElement): void {
    const isActive = this.activeIndex !== 3;
    Utils.handlePopoverBackdropClick(element, this.selectedCrmContactList, 'isActive', isActive, this.cdf);
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }

  toggleChangeAccountRepSidebar() {
    this.showChangeAccountRepModal = !this.showChangeAccountRepModal;
    this.selectedContacts = [];
    this.getAll();
  }

  onArchive(crmDetails: CrmContactListItem, isActive: boolean): void {
    this.selectedCrmContactList = crmDetails;
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: isActive ? MESSAGES.customerActivateConfirmationMessage : MESSAGES.customerDeActivateConfirmationMessage,
      icon: icons.triangle,
      accept: () => {
        this.isArchiveInProgress = true;
        crmDetails.archived = isActive;
        this.showConfirmationDialog = false;
        this.onArchiveConfirmation(crmDetails, isActive);
      },
      reject: () => {
        crmDetails.archived = !isActive;
        this.isArchiveInProgress = false;
        this.showConfirmationDialog = false;
        this.cdf.detectChanges();
        crmDetails.archived = isActive;
        
      }
    });
  }

  private onArchiveConfirmation(crmDetails: CrmContactListItem,isActive): void {
    this.crmService.patch(undefined, `${API_URL_UTIL.admin.crm.contact}/${API_URL_UTIL.admin.crm.archived}/${crmDetails.id}/${!isActive}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          if (crmDetails.archived) {
            this.toasterService.success(MESSAGES.customerActivateSuccess);
          } else {
            this.toasterService.success(MESSAGES.customerDeActivateSuccess);
          }
          
          this.isArchiveInProgress = false;
          this.selectedCrmContactList = null;
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.selectedCrmContactList = null;
          this.getAll();
        }
      });
  }

  isSuperAdminOrManager(): boolean {
    const userRole = this.currentUser?.role.name?.toLowerCase() as RoleNames;
    return userRole === RoleNames.ROLE_SUPER_ADMIN || userRole === RoleNames.ROLE_MANAGER;
  }
}
