import { Constants } from "./app.constants";

export const MESSAGES = {
  changePasswordSuccess: 'Password changed successfully.',
  profileUpdateSuccess: 'Profile updated successfully.',
  vendorAddSuccess: 'Vendor added successfully.',
  vendorUpdateSuccess: 'Vendor updated successfully.',
  supplierAddSuccess: 'Supplier added successfully.',
  supplierUpdateSuccess: 'Supplier updated successfully.',
  vendorArchiveSuccess: 'Vendor is archived successfully.',
  vendorUnArchiveSuccess: 'Vendor is un-archived successfully.',
  supplierArchiveSuccess: 'Supplier is archived successfully.',
  supplierUnArchiveSuccess: 'Supplier is un-archived successfully.',
  dealerUpdateSuccess: 'Dealer updated successfully.',
  dealerAddSuccess: 'Dealer added successfully.',
  dealerArchiveSuccess: 'Dealer is archived successfully.',
  dealerUnArchiveSuccess: 'Dealer is un-archived successfully.',
  userUpdateSuccess: 'User updated successfully.',
  userAddSuccess: 'User added successfully.',
  userArchiveSuccess: 'User is archived successfully.',
  userUnArchiveSuccess: 'User is un-archived successfully.',
  userRoleDeleted: 'Cannot un-archived user, Assigned role has been deleted.',
  deleteWarning: 'Are you sure you want to delete this {record}?',
  deleteContactWarning: 'Are you sure you want to delete this {record}? Deleting this contact will also remove other associated data.',
  archiveWarning: 'Are you sure you want to archive this {record}?',
  changeDefaultAccess: 'Are you sure you want to change defult dealer?',
  unArchiveWarning: 'Are you sure you want to un-archive this {record}?',
  internalServerError: 'There is an issue processing your request. Please try after some time.',
  pipelineConfigAddSuccess: 'Pipeline config added successfully.',
  pipelineConfigUpdateSuccess: 'Pipeline config updated successfully.',
  pipelineConfigDeleteSuccess: 'Pipeline config deleted successfully.',
  shopTaskAddSuccess: 'Task added successfully.',
  shopTaskUpdateSuccess: 'Task updated successfully.',
  shopTaskDeleteSuccess: 'Task deleted successfully.',
  taskActivityLogAddSuccess: 'Activity log added successfully.',
  taskActivityLogUpdateSuccess: 'Activity log updated successfully.',
  taskActivityLogDeleteSuccess: 'Activity log deleted successfully.',
  inventoryAddSuccess: 'Inventory added successfully.',
  inventoryUpdateSuccess: 'Inventory updated successfully.',
  inventoryUnArchiveSuccess: 'Inventory un-archived successfully.',
  inventoryArchiveSuccess: 'Inventory archived successfully.',
  financialUpdateSuccess: 'Financial updated successfully',
  expensesDeleteSuccess: 'Expenses deleted successfully',
  expensesUpdateSuccess: 'Expenses updated successfully',
  expensesAddSuccess: 'Expenses added successfully',
  fileTypeNotSupported: `File type not supported. Allowed file types are: ${Constants.allowedImgFormats}`,
  fileTypeSupportedPDF: `File type not supported. Allowed file types are: ${Constants.allowedPdfFormats}`,
  fileTypeSupportedImgAndPDF: `File type not supported. Allowed file types are:${Constants.allowedImgFormats}, ${Constants.allowedPdfFormats}`,
  fileUploadSuccess: 'Photo uploaded successfully.',
  documentUploadSuccess: 'Document uploaded successfully.',
  fileUploadError: 'File upload error. Please try again later.',
  namesMustBeUnique: 'No duplicate names are allowed.',
  attachmentDeleteSuccess: 'Document deleted successfully.',
  photoDeleteSuccess: 'Photo deleted successfully.',
  fileUploadInProgress: 'File upload is in progress.',
  atleastOneShopRequired: 'At least one shop is required',
  pipelineLocked: 'Pipeline locked successfully.',
  soldTruckDataAddSuccess: 'Sold Truck data added successfully.',
  soldTruckDataUpdateSuccess: 'Sold Truck data updated successfully.',
  statusChangeSuccess: 'Status updated successfully.',
  fillFinancial: 'Please fill financial information first.',
  onlyToAvailable: 'Status can only be changed to Available.',
  onlyToAvailableOrSold: 'Status can only be changed to Available or Sold.',
  commentAddSuccess: 'Comment added successfully.',
  commentDeleteSuccess: 'Comment deleted successfully.',
  commentUpdateSuccess: 'Comment updated successfully.',
  stockTruckDataAddSuccess: 'Stock Truck data added successfully.',
  stockTruckDataUpdateSuccess: 'Stock Truck data updated successfully.',
  soldTruckDataDeleted: 'Sold truck data deleted successfully.',
  stockTruckDataDeleted: 'Stock truck data deleted successfully.',
  taskDeleteWarning: 'Are you sure you want to delete this {record}, It will affect the Pipeline?',
  inventoryGeneralAddSuccess: 'New unit added successfully.',
  inventoryGeneralUpdateSuccess: 'Inventory details updated successfully.',
  makeAddSuccess: 'Make added successfully.',
  unitTypeAddSuccess: 'Unit type added successfully.',
  modelAddSuccess: 'Model added successfully',
  AccountRepUpdateSuccess: 'Account Reporter updated successfully for selected contacts.',
  unAssignAccountRepSuccess: 'Account Reporter unassigned successfully for selected contacts.',
  contactAddSuccess: 'Contact added successfully.',
  BannerAddSuccess: 'Banner added successfully.',
  BannerEditSuccess: 'Banner updated successfully.',
  contactEditSuccess: 'Contact updated successfully.',
  driverScheduleDataAddSuccess: 'Driver schedule added successfully.',
  driverScheduleDataUpdateSuccess: 'Driver schedule updated successfully.',
  driverScheduleDataDeleted: 'Driver schedule deleted successfully',
  incomingTruckDataDeleted: 'Incoming truck deleted successfully',
  driverAddedSuccess: 'Driver added successfully',
  selectDealerError: 'Please select at least one dealer with role',
  notesAddSuccess: 'Notes added successfully',
  notesUpdatedSuccess: 'Notes updated successfully',
  notesDeleteSuccess: 'Notes deleted successfully',
  documentAddSuccess: 'Document added successfully',
  documentUpdateSuccess: 'Document updated successfully',
  documentDeleteSuccess: 'Document deleted successfully',
  incomingTruckDataAddSuccess: 'Incoming truck added successfully.',
  incomingTruckFundingEmail: 'Funding email has been sent successfully.',
  incomingTruckResendFundingEmail: 'Are you sure want to send funding email again?',
  incomingTruckDataUpdateSuccess: 'Incoming truck updated successfully.',
  incomingTruckStatusArrived: 'Are you sure you want to change the status? This will be removed from Incoming Truck Tracking and will only be found in Inventory',
  confirmVendorAddress: 'Inventory address found. Do you want to proceed with vendor address?',
  pipelineOwnerChangeSuccess: 'Pipeline owner updated successfully.',
  roleShouldBeWithPermission: 'Assign some permissions to this role first.',
  crmContactDataDeleted: 'Crm contact deleted successfully.',
  conditionAddSuccess: 'Conditions added successfully',
  conditionUpdateSuccess: 'Conditions updated successfully',
  photoDetailsSuccess: 'Photo details updated successfully',
  photosAddSuccess: 'Photos added successfully',
  maxFileSizeAllowed: 'Max file size allowed is 10MB',
  photoDisplayPicture: 'Display picture selected successfully.',
  cannotUnselectOnlyPhoto: 'At least one display picture is required, so this image cannot be unselected.',
  firstPhotoRemainsSelected: 'One display picture is required, so the first photo remains selected by default.',
  firstPhotoSetAutomatically: 'A display picture is required, so the first photo has been set as the display picture.',
  photoDetailsWarning: 'Please add description before saving.',
  generalSave: 'General saving',
  photosSave: 'Photos saving',
  conditionsSave: 'Conditions saving',
  notesSave: 'Notes saving',
  documentsSave: 'Documents saving',
  soldSave: 'Sold saving',
  financialSave: 'Financial saving',
  customerContactDeleted: 'Contact deleted successfully.',
  customerLeadDataDeleted: 'Customer lead deleted successfully.',
  customerLeadAddSuccess: 'Customer Lead added successfully.',
  customerLeadEditSuccess: 'Customer Lead updated successfully.',
  prospectToCustomerSuccess: 'Contact successfully converted from prospect to customer',
  customerDeActivateSuccess: 'Contact archive successfully.',
  customerActivateSuccess: 'Contact un-archived successfully.',
  customerDeActivateConfirmationMessage: 'Are you sure you want to archive this contact ?',
  customerActivateConfirmationMessage: 'Are you sure you want to un-archived this contact ?',
  inventoryMatchedEmailSuccess: 'Email sent successfully',
  inventoryQuotationPriceBlank: 'Quote price can not be blank.',
  quotationAccepted: 'Quote accepted',
  quotationRejected: 'Quote rejected',
  fileUploadMessage: 'File size must be less than 10MB',
  imageUploadMessage: 'Image size must be less than 10MB',
  totalImageUploadMessage: 'Total upload size for all images must not exceed 50MB',
  mapNotFound: 'Google map not loaded on this address, Please correct the address',
  inventoryMatchingSuccess: 'Inventory matched count updated',
  emailSendSuccess: 'Email send successfully',
  columnNotSelect: 'Please select columns',
  photoUploadSuccess: 'Document uploaded successfully.',
  taskArchiveSuccess: 'Task archived successfully.',
  taskUnArchiveSuccess: 'Task un-archived successfully.',
  deleteSuccessMessage: '{item} deleted successfully',
  removedSuccessMessage: '{item} removed successfully',
  addSuccessMessage: '{item} added successfully',
  updateSuccessMessage: '{item} updated successfully',
  categoryDeleteWarning: 'You can not delete this category as it contains active inventories',
  shopActiveTaskDeleteWarning: 'You can not delete this shop as it is in use in active tasks or users.',
  shopActivePipelineDeleteWarning: 'You can not delete this shop as it is in use in pipeline configurations.',
  addCategoryBeforeAddingUnitType: 'Please select category to add unit type',
  addCategoryBeforeAddingMake: 'Please select category to add make',
  dataHasBeenDeleted: 'This data has been deleted',
  addMakeBeforeAddingModel: 'Please select make to add model',
  addCategoryBeforeAddingModel: 'Please select category to add model',
  addMultipleGroup: 'Are you sure you want to allow this group as multiple group',
  unitTypeDeleteWarning: 'You can not delete this unit type as it contains active inventories',
  makeModelDeleteWarning: 'You can not delete this make/model as it contains active inventories',
  unassignReporterMessage: 'Account report is unassigned for this CRM contact',
  unassignReporterConfirmation: 'Are you sure you want to unassign this CRM contact?',
  unassignReporterForSelectedContactsConfirmation: 'Are you sure you want to unassign Account Reporter for selected CRM contact?',
  assignReporterToSelectedContactsConfirmation: 'Are you sure you want to assign this Account Reporter to selected CRM contact?',
  specificationnFieldOptionsAddedSuccess: 'Specification field options added successfully.',
  statusChangeConfirmation: 'Are you sure you want to update statue',
  formEditPermissionMessage: 'You can not edit this form',
  reminderAddedSuccess: 'Reminder added successfully',
  reminderUpdatedSuccess: 'Reminder updated successfully',
  reminderDeletedSuccess: 'Reminder deleted successfully',
  makeModelAddedSuccess: 'Make/Model added successfully',
  labelWithSameNameExists: 'Label with same name already exists',
  removeAssociation: 'Are you sure you want to remove association?',
  allowMultipleSpecifications: 'Allow multiple preference updated successfully',
  includingPreferenceAllowed: 'Including preference updated successfully',
  selectUnitToCreateQuotation: 'Select at least one stock to send quotation',
  addQuotationForOneStock: 'Please add quote price for at least one selected stock',
  addCustomerEmailForShareDetails: 'Please add customer email',
  soldSelectedItems: 'Are you sure you want to sell only the selected inventories? This action will de-associate remaining inventories.',
  holdSelectedItems: 'Are you sure you want to hold only the selected inventories? This action will de-associate remaining inventories.',
  sendQuotationForSelectedItems: 'Are you sure you want to send quotation for selected inventories only? This action will de-associate remaining inventories if this quotation is accepted.',
  disableEditRole: 'You cannot edit system default role.',
  disableDeleteRole: 'You cannot delete system default role.',
  duplicatesNotAllowed: 'Duplicate shops not allowed.',
  fillAllIncomingTruckData: 'Please fill all incoming truck data to download PDF',
  emailConfigUpdated: 'Email configurations updated successfully',
  preferenceMessage: 'Preference has been {action} successfully.',
  preferenceDeleteWarning: 'Are you sure, .',
  updatePreferenceWarning: 'Are you sure you want to update {record} preference?',
  pageUrlCopiedSuccess: 'URL has been copied successfully! You can now share it.',
  pageUrlCopiedFailed: 'Failed to copy URL. Please try again.',
  quoteFormSubmitted: 'Your details submitted successfully.',
  deleteQuoteFieldWarning: 'Are you sure you want to delete this {record} field?',
  addQuoteFieldSuccess: '{record} field added successfully.',
  updateQuoteFieldSuccess: '{record} field updated successfully.',
  deleteQuoteFieldSuccess: '{record} field deleted successfully.',
  addQuoteFieldErrorMessage: '{record} field already exists.',
  updateNotifyPersonsSuccess: 'Notify persons updated successfully.',
  disableDeleteField: 'You cannot delete system default Field.',
  updateFundingEmailConfigSuccess: 'Funding email fields updated successfully.',
  retailAskingPriceMessage: 'Would you like to update the retail price as you have associated the inventory',
  updateDisplayOnWebWarning: 'Are you sure you {action} display this inventory on public page?',
  updateDisplayOnWebSuccess: 'This inventory {action} display on public page',
  cantAssociatedForSoldInventory: 'You cannot associate an inventory unit that has been marked as sold. If you would like to associate an inventory item, please change its status to something other than sold',
  quotationSendSuccess: 'Quotation has been sent on the email.',
  archivedContact: 'Archived items are not selectable.',
  specificationFailed: 'An error occurred while saving the specification details. Please try again.',
  noDocumentsAvailable: 'No documents available to download.'
}
