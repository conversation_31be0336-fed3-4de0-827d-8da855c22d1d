<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn class="top-header">
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button class="btn btn-primary left" (click)="onAdd()" [appImageIconSrc]="constants.staticImages.icons.addNew" *appHasPermission="[permissionActions.CREATE_SALES_TASK]">
      <span class="show-label">Add New Task</span>
    </button>
    <button class="btn btn-primary left column-btn show-label" (click)="toggleFilterSidebar()">
      <span class="show-label">Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
  </div>
</app-page-header>

<div class="card tabs crm-task-list">
  <div class="tab-content">
    <p-tabView (onChange)="onTabChanged($event)" [scrollable]="true" [(activeIndex)]="activeIndex">
      <p-tabPanel header="All Tasks">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
        </ng-template>
      </p-tabPanel>
      <p-tabPanel header="My Tasks">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
        </ng-template>
      </p-tabPanel>
      <p-tabPanel header="Archived">
        <ng-template pTemplate="content">
          <ng-container [ngTemplateOutlet]="activeTabTemplate"></ng-container>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>
</div>

<ng-template #activeTabTemplate>
  <p-table
    [columns]="selectedColumns"
    [value]="tasks"
    responsiveLayout="scroll"
    sortMode="single"
    [customSort]="true"
    [lazy]="true"
    [reorderableColumns]="true"
    (onLazyLoad)="onSortChange($event, getAll.bind(this))"
    [sortField]="paginationConfig.predicate"
    [rowHover]="true"
    [loading]="isLoading"
    styleClass="p-datatable-gridlines"
    [resizableColumns]="true"
    columnResizeMode="expand"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <ng-container *ngFor="let col of columns">
          <th
            pResizableColumn
            class="min-width-100 small-col"
            *ngIf="col.disable && col.name === 'ID'"
            [pSortableColumn]="col?.shortingKey"
            pReorderableColumn
            [pReorderableColumnDisabled]="true"
            scope="col"
          >
            {{ col.name }} <p-sortIcon [field]="col?.shortingKey"> </p-sortIcon>
          </th>
          <th pResizableColumn [pSortableColumn]="col?.shortingKey" [pSortableColumnDisabled]="!col.shorting" pReorderableColumn *ngIf="!col.disable && col.name !== 'Active'">
            {{ col.name }}
            <span *ngIf="col.shorting">
              <p-sortIcon [field]="col.shortingKey" *ngIf="col.shortingKey"></p-sortIcon>
            </span>
          </th>
          <th
            pResizableColumn
            [pSortableColumn]="col?.shortingKey"
            [pSortableColumnDisabled]="!col.shorting"
            pReorderableColumn
            *ngIf="!col.disable && col.name === 'Active' && activeIndex !== 2"
          >
            {{ col.name }}
            <span *ngIf="col.shorting">
              <p-sortIcon [field]="col.shortingKey" *ngIf="col.shortingKey"></p-sortIcon>
            </span>
          </th>
          <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="small-col min-width-100 cursor-default" *ngIf="col.disable && col.name === 'Action'">
            {{ col.name }}
          </th>
        </ng-container>
      </tr>
      <tr class="inventory-search-tr">
        <ng-container *ngFor="let col of columns">
          <th pResizableColumn class="small-col min-width-100" *ngIf="col.disable && col.name === 'ID'" scope="col">
            <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /> </span>
          </th>
          <th pResizableColumn *ngIf="!col.disable && col.name !== 'Active'" class="small-col min-width-100" scope="col">
            <span class="search-input" *ngIf="col.type === 'DROP_DOWN' && col.name === 'Status'">
              <p-dropdown
                appPreventClearFilter
                [options]="taskStatusesCopy"
                [(ngModel)]="col.value"
                (onChange)="tableSearchByColumn($event, col)"
                class="w-145 crm-task-list"
                optionLabel="name"
                optionValue="name"
                [filter]="true"
                filterBy="name"
                [showClear]="col.value ? true : false"
                placeholder="Select status"
                appendTo="body"
              >
                <ng-template pTemplate="selectedItem">
                  <div class="country-item country-item-value" *ngIf="col.value">
                    <div>{{ col.value }}</div>
                  </div>
                </ng-template>
                <ng-template let-status pTemplate="item">
                  <div class="country-item">
                    <div>{{ status.name }}</div>
                  </div>
                </ng-template>
              </p-dropdown>
            </span>

            <span class="search-input" *ngIf="col.type !== 'DROP_DOWN' && col.type !== 'IMAGE' && col.type !== 'BUTTON' && col.type !== 'DATE'">
              <input pInputText type="text" class="form-control" [(ngModel)]="col.value" (input)="tableSearchByColumn($event.target, col)" />
            </span>
            <span class="search-input" *ngIf="col.type === 'DATE' && col.key !== 'startDate/endDate'">
              <p-calendar
                appendTo="body"
                [showIcon]="true"
                [showButtonBar]="true"
                [readonlyInput]="true"
                inputId="startDateIcon"
                [(ngModel)]="createdDate"
                (onSelect)="tableSearchByColumn($event, col)"
                (onClearClick)="clearDate(col.key)"
              ></p-calendar>
            </span>
            <span class="search-input" *ngIf="col.type === 'DATE' && col.key === 'startDate/endDate'">
              <p-calendar
                #calendar
                [(ngModel)]="rangeDates"
                appendTo="body"
                [showIcon]="true"
                [showButtonBar]="true"
                [readonlyInput]="true"
                inputId="range"
                (onSelect)="tableSearchByColumn($event, col)"
                (onClearClick)="clearDate(col.key)"
              ></p-calendar>
            </span>
          </th>
          <th pResizableColumn class="small-col min-width-100" *ngIf="col.name === 'Active' && activeIndex !== 2" scope="col"></th>
          <th pResizableColumn class="small-col reset-icon min-width-100" *ngIf="col.disable && col.name === 'Action'">
            <button type="button" class="btn btn-primary btn-sm reset-btn" (click)="clearSearchInput()">Reset filters</button>
          </th>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-columns="columns" let-rowData let-rowIndex="rowIndex">
      <tr>
        <ng-container *ngFor="let col of columns">
          <td class="d-flex priority-icon min-width-100" *ngIf="col.name === 'ID' && col.disable" (click)="onViewEdit(rowData, false)" class="view-task">
            {{ getEvaluatedExpression(col.key, rowData) }}
          </td>
          <ng-container *ngIf="col.type !== 'DROP_DOWN' && !col.disable && col.type !== 'IMAGE' && col.type !== 'BUTTON' && col.key !== 'startDate/endDate'">
            <td>
              <span *ngIf="col.type === 'DATE'">
                {{ getEvaluatedExpression(col.key, rowData) | date : constants.dateFormat }}
              </span>
              <span *ngIf="col.type !== 'DATE'">
                {{ getEvaluatedExpression(col.key, rowData) }}
              </span>
            </td>
          </ng-container>
          <td *ngIf="col.type === 'DROP_DOWN' && col.name === 'Status'" class="min-width-100">
            <p-dropdown
              class="w-145 crm-task-list"
              appendTo="body"
              [options]="taskStatuses"
              [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_SALES_TASK]) || rowData.archived"
              (click)="findStatusIndex(rowIndex)"
              optionLabel="name"
              [(ngModel)]="rowData.taskStatus.id"
              optionValue="id"
              (onChange)="changeStatus(rowData, rowData?.taskStatus?.id, rowData?.id, $event)"
            >
              {{ rowData?.taskStatus?.name }}
            </p-dropdown>
          </td>
          <td *ngIf="col.type === 'DATE' && col.key === 'startDate/endDate'" class="timeline">
            <span> {{ rowData?.endDate | date : constants.dateFormat }}</span>
          </td>
          <td *ngIf="col.type === 'BUTTON' && activeIndex !== 2">
            <ui-switch
              [(ngModel)]="rowData.isActive"
              [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_SALES_TASK])"
              [loading]="selectedTask?.id === rowData.id && isArchiveInProgress"
              (change)="onArchive(rowData, $event)"
            >
              <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedTask?.id === rowData.id"></fa-icon>
            </ui-switch>
          </td>

          <td *ngIf="col.name === 'Action' && col.disable" class="min-width-100">
            <div class="actions-content">
              <ng-container *ngIf="activeIndex !== 2">
                <img [src]="constants.staticImages.icons.edit" (click)="onViewEdit(rowData, true)" alt="" *appHasPermission="[permissionActions.UPDATE_SALES_TASK]" />
              </ng-container>
              <em class="pi pi-trash text-danger" appShowLoaderOnApiCall (click)="onDelete(rowData, $event)" alt="" *appHasPermission="[permissionActions.DELETE_SALES_TASK]"> </em>
            </div>
          </td>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
    </ng-template>
  </p-table>
  <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
</ng-template>
<p-sidebar
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false; taskId = ''"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-crm-task-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [activeIndex]="activeIndex" [taskId]="taskId" [isViewMode]="isViewMode"></app-crm-task-add>
</p-sidebar>

<p-sidebar
  [(visible)]="showColumnModal"
  position="right"
  (onHide)="showColumnModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  [style]="{ width: '30em' }"
>
  <app-column-dropdown
    (onClose)="toggleColumnSidebar()"
    *ngIf="showColumnModal"
    [isModelVisible]="showColumnModal"
    [privateFilter]="defaultColumnsArray"
    [filterParams]="getFilterSaveParams()"
    [columnsList]="dropDownColumnList"
    (onSubmitCheck)="callFilterApiAgain()"
  >
  </app-column-dropdown>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [modulesName]="historyModulesName"></app-recent-activity>
</p-sidebar>

<p-confirmPopup *ngIf="!showConfirmationDialog" appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>
<p-confirmDialog styleClass="confirm-dialog" header="Confirmation" *ngIf="showConfirmationDialog" [breakpoints]="{ '960px': '60vw', '640px': '90vw' }" [style]="{ width: '30vw' }">
</p-confirmDialog>
