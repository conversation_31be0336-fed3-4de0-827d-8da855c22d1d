import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { dateFormat } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';
import { ModelType } from '@pages/inventory/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { SoldTruckService } from '@pages/pipeline/pages/sold-truck-board/sold-truck.service';
import { ReportModuleConst } from '@pages/reports/models/reports-const';
import { SalesDateRangeFilter } from '@pages/reports/pages/crm-reports/models/daily-sales';
import { ReportingService } from '@pages/reports/services/reporting-service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
import { InventoryAgingListItem, colorCodes } from '../../models/inventory-report-model';

@Component({
  selector: 'app-inventory-aging',
  templateUrl: './inventory-aging.component.html',
  styleUrls: ['./inventory-aging.component.scss']
})
export class InventoryAgingComponent extends BaseComponent implements OnInit {
  _selectedColumns: ColumnItem[] = [];
  acquisitionType: IdNameModel[] = [];
  inventoryAgingList: InventoryAgingListItem[] = []
  filterParams: GenericFilterParams = new GenericFilterParams();
  globalSearch = new Subject<FilterValue[]>();
  dropDownColumnList: ColumnItem[] = [];
  salesRepList: IdNameModel[] = [];
  accountRepIds: Array<number> = [];
  vendorId!: number | null;
  acquisitionMethodId!: number | null;
  vendors: IdNameModel[] = [];
  dateFilterFormGroup!: FormGroup;
  showListModal = false;
  colorCodes = colorCodes;
  dropdownLoaders = {
    salesRep: false,
    acquisitionType: false,
    vendors: false
  };
  constructor(
    private readonly commonService: CommonService,
    private readonly cdf: ChangeDetectorRef,
    private readonly expensesService: ExpensesService,
    private readonly soldTruckService: SoldTruckService,
    private readonly reportingService: ReportingService,
    private readonly datePipe: DatePipe,

    private readonly fb: FormBuilder

  ) {
    super();
    this.pageTitle = 'Inventory Aging Report';
    this.paginationConfig.itemsPerPage = 25;
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }
  set selectedColumns(val: any[]) {
    this._selectedColumns = this.dropDownColumnList.filter(col => val.includes(col));
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getDropDownColumnList();
    this.displaySearchResult();
    this.getAllSearchOptions();
    this.onSubmit();
  }

  private getAllSearchOptions(): void {
    this.getAccountReportsList();
    this.getAcquisitionList();
    this.getVendorList();
  }


  private initializeFormGroup(): void {
    this.dateFilterFormGroup = this.fb.group({
      startDateGroup: this.startDateFormGroup,
      endDateGroup: this.endDateFormGroup,
    });
  }

  private get startDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(new Date()),
      key: new FormControl(SalesDateRangeFilter.transDate),
      dataType: new FormControl(SalesDateRangeFilter.date),
      operator: new FormControl(SalesDateRangeFilter.greaterThanEqual),
    })
  }

  private get endDateFormGroup(): FormGroup {
    return this.fb.group({
      value: new FormControl(new Date()),
      key: new FormControl(SalesDateRangeFilter.transDate),
      dataType: new FormControl(SalesDateRangeFilter.date),
      operator: new FormControl(SalesDateRangeFilter.lessThanEqual),
    })
  }

  get dailySalesEndDateFormGroup(): FormGroup {
    return this.dateFilterFormGroup.get('endDateGroup') as FormGroup;
  }

  get dailySalesStartDateFormGroup(): FormGroup {
    return this.dateFilterFormGroup.get('startDateGroup') as FormGroup;
  }

  private getDropDownColumnList(): void {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${ReportModuleConst.INVENTORY_AGING_REPORT}`);
    this.commonService.getListFromObject<ColumnItem[]>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        this._selectedColumns = response;
        this.cdf.detectChanges();
      });
  }

  private getAccountReportsList(): void {
    this.dropdownLoaders.salesRep = true;
    this.soldTruckService.getPipelineOwnerList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (salesRepList) => {
        this.salesRepList = salesRepList;
        this.dropdownLoaders.salesRep = false;
      },
      error: () => {
        this.dropdownLoaders.salesRep = false;
      }
    });
  }

  private displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
      this.getAll();
    });
  }

  getAll() {
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    const endpoint = `${API_URL_UTIL.reports.inventoryAging}/${API_URL_UTIL.reports.filter}`;
    this.reportingService.getListWithFiltersWithPagination<GenericFilterParams, InventoryAgingListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.inventoryAgingList = res.content;
          this.setPaginationParamsFromPageResponse<InventoryAgingListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    const endpoint = `${API_URL_UTIL.reports.inventoryAging}/${API_URL_UTIL.reports.filter}`;
    this.reportingService.getListWithFiltersWithPagination<GenericFilterParams, InventoryAgingListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, endpoint)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Inventory_Aging_Report");
          this.isExporting = false;
        });
      });
  }

  getExcelData(data: Array<InventoryAgingListItem>) {
    return data.map(res => ({
      'Stock#': res?.stockNumber,
      'Amount': Utils.formatCurrency(res?.amount),
      'Acquisition Method': res?.acquisitionMethod?.name,
      'Contact/Vendor/Supplier': this.getVendorName(res),
      'Inventory Age': res?.inventoryAge,
      'Purchased By': res?.purchasingAgent?.name,
      'Purchase Date': Utils.dateIntoUserReadableFormat(res.transDate ? new Date(res.transDate).toLocaleDateString().replace(/\//g, '-') : ''),
    }));
  }

  private getVendorName(res: InventoryAgingListItem): string {
    let name: string | undefined;
    if (!res.contactAndVendorAndSupplierType) {
      return '';
    }
    switch (res.contactAndVendorAndSupplierType.toUpperCase()) {
      case ModelType.VENDOR.toUpperCase():
        name = res.vendor?.name;
        break;
      case ModelType.SUPPLIER.toUpperCase():
        name = res.supplier?.name;
        break;
      case ModelType.CONTACT.toUpperCase():
        name = res.crmContact?.name;
        break;
      default:
        name = '';
    }
    return name ?? '';
  }

  tableSearchByColumn(event: any, col: any): void {
    this.isLoading = true;
    this.inventoryAgingList = [];
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(values => {
        if (Array.isArray(values.value) && !values.value.length) {
          return false;
        }
        return true;
      });
    }
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  private setValueForReset(input: string, col: ColumnItem): void {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else {
      if (temp) {
        temp.value = input;
      }
    }
    if (temp1) {
      temp1.value = temp?.value;
    }

  }

  private getEventValue(event: any, col: ColumnItem): string {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.searchKey === 'status' ? event?.value?.split()?.join('_')?.toUpperCase() : event?.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  private setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  private getFilterInfo(inputValue: any, col: any): void {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.searchKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.searchKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        // TODO this will be removed once confirm
        // if (inputValue?.length) {
        if (inputValue) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
  }

  private assignOperator(type: string): string {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
      case 'DOUBLE':
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }


  private assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN;
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER;
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE;
        break;
      case 'DATE':
        stringDataType = DataType.DATE;
        break;
      case 'DROP_DOWN':
        stringDataType = col?.searchKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG;
        break;
      default:
        stringDataType = DataType.STRING;
        break;
    }
    return stringDataType
  }

  private setSearchEndDate(event: any, col: any): void {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.key,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  private setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  private getAcquisitionList(): void {
    this.dropdownLoaders.acquisitionType = true;
    this.expensesService.getAcquisitionList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (acquisitionType) => {
        this.acquisitionType = acquisitionType;
        this.dropdownLoaders.acquisitionType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.dropdownLoaders.acquisitionType = false;
      }
    });
  }

  private getVendorList(): void {
    this.dropdownLoaders.vendors = true;
    this.commonService.getList(API_URL_UTIL.vendorsContactsSuppliers).pipe(takeUntil(this.destroy$)).subscribe({
      next: (vendors) => {
        vendors.map((d: any) => {
          d.name = `${d.name} (${d.type})`;
          d.id = `${d.id}-${d.type}`;
          return d
        })
        this.vendors = vendors;
        this.dropdownLoaders.vendors = false;
      },
      error: () => {
        this.dropdownLoaders.vendors = false;
      }
    });
  }

  getContactVendorSupplier(data: InventoryAgingListItem): string {
    switch (data.contactAndVendorAndSupplierType) {
      case ModelType.VENDOR.toUpperCase():
        return data.vendor?.name ?? '';
      case ModelType.CONTACT.toUpperCase():
        return data.crmContact?.name ?? '';
      case ModelType.SUPPLIER.toUpperCase():
        return data.supplier?.name ?? '';
      default:
        return '';
    }
  }

  handleVendorSearch(event: any): void {
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(value => {
        return !(value.key === 'vendor.id' || value.key === 'crmContact.id' || value.key === 'supplier.id')
      })
    }
    if (event.value) {
      const type = event.value.split("-")[1];
      const id = event.value.split("-")[0];
      this.filterParams.values = this.filterParams?.values?.length ? this.filterParams.values : [];
      this.filterParams.values.push(this.purchaseBySearchFilter(id, type.toUpperCase()))
    }
    this.getAll();
  }

  private purchaseBySearchFilter(id: number, type: string): FilterValue {
    const filter = new FilterValue();
    filter.dataType = DataType.LONG;
    filter.value = String(id);
    filter.operator = OperatorType.EQUAL;
    if (type === ModelType.VENDOR.toUpperCase()) {
      filter.key = 'vendor.id'
    } else if (type === ModelType.CONTACT.toUpperCase()) {
      filter.key = 'crmContact.id'
    } else {
      filter.key = 'supplier.id'
    }
    return filter;
  }

  clearSalesRep(col: ColumnItem): void {
    this.accountRepIds = [];
    this.tableSearchByColumn([], col);
  }

  clearSearchInput(): void {
    this.dateFilterFormGroup.reset();
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.vendorId = null;
    this.acquisitionMethodId = null;
    this.accountRepIds = this.filterParams.values = [];
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  private dateFilterValues(inputDate: string, isStartingDate: boolean): FilterValue {
    const date = new Date(inputDate);
    let finalDate;
    if (isStartingDate) {
      finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0).toISOString();
    } else {
      finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50).toISOString();
    }
    return {
      value: finalDate,
      key: SalesDateRangeFilter.transDate,
      dataType: DataType.DATE,
      operator: isStartingDate ? OperatorType.GREATER_THAN_OR_EQUAL : OperatorType.LESS_THAN_OR_EQUAL,
    };
  }

  onSubmit(): void {
    if (this.filterParams.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(value => {
        return value.key !== SalesDateRangeFilter.transDate;
      })
    } else {
      this.filterParams.values = []
    }
    if (this.dateFilterFormGroup.controls.startDateGroup.value.value) {
      this.filterParams.values.push(this.dateFilterValues(this.dateFilterFormGroup.controls.startDateGroup.value.value, true));
    }
    if (this.dateFilterFormGroup.controls.endDateGroup.value.value) {
      this.filterParams.values.push(this.dateFilterValues(this.dateFilterFormGroup.controls.endDateGroup.value.value, false));
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    this.getAll();
  }

  getClass(ageData: InventoryAgingListItem): string {
    if (ageData.inventoryAge <= 90) {
      return this.colorCodes.LESS_THAN_90;
    } else if (ageData.inventoryAge <= 180) {
      return this.colorCodes.LESS_THAN_180;
    } else if (ageData.inventoryAge <= 270) {
      return this.colorCodes.LESS_THAN_270;
    } else if (ageData.inventoryAge <= 365) {
      return this.colorCodes.LESS_THAN_365;
    } else {
      return this.colorCodes.GREATER_THAN_365;
    }
  }
}
