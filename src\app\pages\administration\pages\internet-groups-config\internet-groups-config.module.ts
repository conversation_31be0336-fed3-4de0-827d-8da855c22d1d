import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { NgxMaskModule } from 'ngx-mask';
import { ConfirmationService } from 'primeng/api';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DropdownModule } from 'primeng/dropdown';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { SharedComponentsModule } from 'src/app/@shared/components/shared-components.module';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from 'src/app/@shared/pipes/pipes.module';
import { InternetGroupsConfigAddUpdateComponent } from './internet-groups-config-add-update/internet-groups-config-add-update.component';
import { InternetGroupsConfigListComponent } from './internet-groups-config-list/internet-groups-config-list.component';
import { InternetGroupsConfigComponent } from './internet-groups-config.component';

const routes: Routes = [
  {
    path: '',
    component: InternetGroupsConfigComponent,
    title: 'Skeye - Shadow Groups Config',
    children: [
      {
        path: '',
        component: InternetGroupsConfigListComponent,
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  declarations: [
    InternetGroupsConfigComponent,
    InternetGroupsConfigListComponent,
    InternetGroupsConfigAddUpdateComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeModule,
    TableModule,
    DropdownModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    NgxMaskModule.forRoot(),
    PipesModule,
    CheckboxModule
  ],
  providers: [
    ConfirmationService
  ],
  exports: [InternetGroupsConfigAddUpdateComponent]
})
export class InternetGroupsConfigModule { }
