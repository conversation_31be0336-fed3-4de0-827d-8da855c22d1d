<div class="modal-title" [ngClass]="this.isEditMode ? 'edit-header' : ''">
  <h4 class="header-title">{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>

<form [formGroup]="internetGroupForm" (ngSubmit)="onSubmit()">
  <section class="content">
    <div class="card p-3">
      <div class="row">
        <div class="col-12 mb-2">
          <label for="name" class="form-label required">Name</label>
          <input type="text" id="name" class="form-control" formControlName="name" placeholder="Enter shadow group name" />
          <app-error-messages [control]="internetGroupForm.controls.name"></app-error-messages>
        </div>
      </div>
    </div>
  </section>
  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall [disabled]="internetGroupForm.invalid">Save</button>
  </div>
</form>
