import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Constants, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Contact, CrmContactListItem } from '@pages/administration/models/crm.model';
import { Account } from '@pages/auth/models';
import { User } from '@pages/calendar/event-calendar/models/evnet-calendar';
import { CrmService } from '@pages/crm/services/crm.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { Address } from 'ngx-google-places-autocomplete/objects/address';
import { takeUntil } from 'rxjs';
import { RoleNames } from 'src/app/@shared/models';

@Component({
  selector: 'app-crm-contact-add',
  templateUrl: './crm-contact-add.component.html',
  styleUrls: ['./crm-contact-add.component.scss']
})
export class CrmContactAddComponent extends BaseComponent implements OnInit, OnChanges {
  title = 'Add Contact';
  contactFormGroup!: FormGroup;
  hasDataBeenModified = false;
  showEditBtn = true;
  isEditMode = false;
  crmContactDetails!: any;
  redirectUrl!: string;
  activeIndex = 0;
  @Input() crmContactInfo!: CrmContactListItem | null;
  @Input() currentUser!: Account | null
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() isViewMode!: boolean | null;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  AccountRepList: User[] = [];
  loaders = {
    accountRep: false
  }
  contactNoFormat = Constants.phoneNumberMask;
  constructor(
    private readonly fb: FormBuilder,

    private readonly userAnnotationService: UserAnnotationService,
    private readonly router: Router,
    private readonly commonSharedService: CommonSharedService,
    private readonly crmService: CrmService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly activeRoute: ActivatedRoute,
  ) { super(); }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getIdByContact();
    this.getUsers();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
  }


  getUsers(): void {
    this.loaders.accountRep = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.AccountRepList = res;
          this.loaders.accountRep = false;
        },
        error: () => {
          this.loaders.accountRep = false;
        }
      });
  }

  getIdByContact(): void {
    this.isLoading = true;
    if (this.crmContactInfo) {
      this.commonSharedService.setBlockUI$(true);
      const endpoint = API_URL_UTIL.admin.crm.contact;
      this.crmService.get(Number(this.crmContactInfo.id), endpoint).pipe(takeUntil(this.destroy$)).subscribe((crmContactDetails) => {
        this.crmContactDetails = crmContactDetails;
        this.setCrmContactInfoInFormGroup();
        this.showEditBtn = this.allowActions();
        this.isLoading = false;
        this.commonSharedService.setBlockUI$(false);
        this.cdf.detectChanges();
      });
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.crmContactInfo.currentValue) {
      this.title = this.isViewMode ? 'View Contact' : 'Edit Contact';
      this.isEditMode = !this.isViewMode;
    }
  }

  initializeFormGroup(): void {
    this.contactFormGroup = this.fb.group({
      id: new FormControl(''),
      jobTitle: new FormControl(''),
      company: new FormControl(''),
      department: new FormControl(''),
      firstName: new FormControl('', [Validators.required]),
      lastName: new FormControl('', [Validators.required]),
      primaryEmail: new FormControl('', [Validators.required, Validators.email]),
      secondaryEmail: new FormControl('', [Validators.email]),
      primaryPhone: new FormControl('', [Validators.required]),
      secondaryPhone: new FormControl(''),
      webSite: new FormControl(''),
      fax: new FormControl(''),
      streetAddress: new FormControl('', [Validators.required]),
      city: new FormControl('', [Validators.required]),
      state: new FormControl('', [Validators.required]),
      zipcode: new FormControl('', [Validators.required]),
      county: new FormControl('', [Validators.required]),
      country: new FormControl('', [Validators.required]),
      notes: new FormControl(''),
      latitude: new FormControl(''),
      longitude: new FormControl(''),
      accountReporterId: new FormControl(null)
    });
  }

  get newAddressFormGroup(): FormGroup {
    return this.fb.group({
      streetAddress: new FormControl(''),
      city: new FormControl('', [Validators.required]),
      state: new FormControl('', [Validators.required]),
      zipcode: new FormControl('', [Validators.required]),
      county: new FormControl(null, [Validators.required]),
      country: new FormControl(null, [Validators.required]),
      latitude: new FormControl(''),
      longitude: new FormControl('')
    });
  }

  get addressFormGroup(): FormGroup {
    return this.contactFormGroup.get('address') as FormGroup;
  }

  get vendorInfoCreateParams(): Contact {
    return {
      ...this.contactFormGroup.value,
      id: this.crmContactInfo?.id
    };
  }

  saveContact(close = true): void {
    this.crmService.add(this.vendorInfoCreateParams, API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.contactAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.contactFormGroup.reset();
      }
    });
  }

  editContact(): void {
    this.crmService.update(this.vendorInfoCreateParams, API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.contactEditSuccess : MESSAGES.contactAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.contactFormGroup.enable();
      this.isViewMode = false;
      this.isEditMode = true;
      this.title = 'Edit Contact'
      return;
    }
    this.contactFormGroup.updateValueAndValidity();
    if (this.contactFormGroup.invalid) {
      this.contactFormGroup.markAllAsTouched();
      return;
    }
    if (this.isEditMode) {
      this.editContact();
    }
    else {
      this.saveContact(close);
    }
  }

  allowActions(): boolean {
    if (!this.crmContactDetails?.accountReporterId) {
      return true;
    }
    if (this.currentUser?.role.name.toLowerCase() === RoleNames.ROLE_SALESPERSON && this.currentUser?.id !== this.crmContactDetails?.accountReporterId) {
      return false;
    }
    return true;
  }

  private setCrmContactInfoInFormGroup(): void {
    if (this.crmContactDetails) {
      this.contactFormGroup.patchValue({
        firstName: this.crmContactDetails.firstName,
        lastName: this.crmContactDetails.lastName,
        company: this.crmContactDetails.company,
        jobTitle: this.crmContactDetails.jobTitle,
        department: this.crmContactDetails.department,
        primaryEmail: this.crmContactDetails.primaryEmail,
        secondaryEmail: this.crmContactDetails.secondaryEmail,
        personalEmail: this.crmContactDetails.personalEmail,
        webSite: this.crmContactDetails.webSite,
        primaryPhone: this.crmContactDetails.primaryPhone,
        secondaryPhone: this.crmContactDetails.secondaryPhone,
        fax: this.crmContactDetails.fax,
        notes: this.crmContactDetails.notes,
        streetAddress: this.crmContactDetails.streetAddress,
        city: this.crmContactDetails.city,
        state: this.crmContactDetails.state,
        zipcode: this.crmContactDetails.zipcode,
        county: this.crmContactDetails.county,
        country: this.crmContactDetails.country,
        latitude: this.crmContactDetails.latitude,
        longitude: this.crmContactDetails.longitude,
        accountReporterId: this.crmContactDetails?.accountReporterId

      });
      if (this.isViewMode) {
        this.contactFormGroup.disable();
      }
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress()
  }

  handleAddressChange(address: Address | any) {
    if (address?.address_components) {
      this.contactFormGroup.controls['streetAddress'].setValue(address.name);
      this.contactFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.contactFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'country':
            this.contactFormGroup.controls['country'].setValue(addressComponent.long_name);
            break;
          case 'locality':
            this.contactFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.contactFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_2':
            this.contactFormGroup.controls['county'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.contactFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  getFullAddress() {
    const rowAddress = []
    const address = this.contactFormGroup.value;
    rowAddress.push(address?.streetAddress ?? '')
    rowAddress.push(address?.city ?? '')
    rowAddress.push(address?.state ?? '')
    rowAddress.push(address?.zipcode ?? '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }
}
