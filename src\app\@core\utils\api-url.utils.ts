
const softDelete = 'soft-delete';
export const API_URL_UTIL = {
  account: {
    root: 'account',
    loginInit: 'login/init',
    loginFinish: 'login/finish',
    loginWithEmailPassword: 'authenticate',
    forgotPasswordInit: 'account/reset-password/init',
    forgotPasswordFinish: 'account/reset-password/finish',
    refreshAccessToken: 'token/refresh',
  },
  settings: {
    changePassword: 'account/change-password',
  },
  admin: {
    users: {
      root: 'skeye-users',
      list: 'filter',
      register: 'register',
      changePassword: 'admin/change-password',
      role: 'skeye-users/role/:roleName',
      make: 'make',
      changeActiveDealer: 'update-currently-active-dealer'
    },
    vendors: {
      root: 'vendors',
      list: 'filter'
    },
    suppliers: {
      root: 'suppliers',
      list: 'filter'
    },
    advertising: {
      root: 'advertising',
    },
    quoteFormConfig: {
      root: 'quote-configurations',
    },
    dealers: {
      root: 'dealers',
      list: 'filter',
      basicInfos: 'basic-info',
      shops: 'shops',
      basicInfo: 'basic-info',
      allAndInactiveForCurrentUser: 'skeye-user'
    },
    roles: {
      root: 'roles'
    },
    internetGroups: {
      root: 'internet-groups'
    },
    assets: {
      root: 'assets'
    },
    modules: {
      root: 'modules'
    },
    pipelineConfig: {
      root: 'template-pipelines',
      list: 'filter',
      softDelete: softDelete
    },
    shops: {
      root: 'shops',
      users: 'users-by-shop',
      basicInfo: 'basic-info',
      shops: '/shops'
    },
    crm: {
      root: 'crm',
      contact: 'contacts',
      filter: 'contacts/filter',
      customerFilter: 'customer-leads/filter',
      quatationFilter: 'quotations/filter',
      customerStatus: ':customerId/status?',
      customer: 'customer-leads/',
      customerLeadsById: 'customer-leads/:id',
      customerInventoryMatched: 'units/quotations/filter/list',
      customerLead: 'customer-leads',
      quotations: 'quotations',
      acceptQuotation: 'quotations/:quotationId/quotation-status',
      customerMatchedCount: ':customerId/matchingCount?',
      removeReporter: ':customerId/remove/reporter',
      reporter: 'reporter',
      crmTask: 'crm-task',
      reminders: 'crm-contact-reminders',
      reminderHistory: 'reminder-history',
      documents: 'crm/contacts/document',
      list: 'list',
      archived: 'archived',
    },
    categorySpecification: {
      root: 'unit-type-categories',
      categoryCount: 'count'
    },
    unitSpecification: {
      root: 'units',
      unitTypes: 'types',
      unitModels: 'models',
      delete: 'delete'
    },
    makeSpecification: {
      root: 'makes',
      category: 'category',
      count: 'count',
      id: 'id'
    },
    specifications: {
      root: 'specification-masters',
      specificationByCategoryId: 'category/:id'
    }
  },
  tasks: {
    root: 'tasks',
    list: 'filter',
    stockNumbers: 'stock-numbers',
    types: 'types',
    basicInfo: 'basic-info',
    priorities: 'priorities/options',
    statuses: 'statuses',
    id: ':id',
    userGroups: 'user-groups',
    taskComments: ':taskId/comments',
    comments: 'comments',
    taskActivityLogs: ':taskId/activity-logs',
    activityLogs: 'activity-logs',
    attachments: 'attachments',
    delete: softDelete,
    status: ':taskId/status',
    archived: 'tasks/archived',
    allTasks: 'list/all'
  },
  inventory: {
    root: 'units',
    inventory: 'inventory',
    list: 'filter',
    search: 'search',
    photos: 'photos',
    inventoryDetails: ':unitId',
    inventoryPhotos: ':unitId/unit-images',
    generalInfo: ':unitId/general-information',
    statuses: 'statuses',
    status: ':unitId/status',
    archive: ':unitId/archive',
    archiveArray: 'archive',
    unitType: 'types',
    models: 'models',
    notes: 'notes',
    notesDetails: ':unitId/notes',
    document: 'documents',
    downloadDocuments: 'download/documents',
    documentDetails: ':unitId/documents',
    documentDelete: 'document/:documentId',
    components: 'components',
    componentConditions: 'component-conditions',
    componentsDetails: ':unitId/component-conditions',
    images: 'images',
    photoDetail: 'images/:unitImageId/detail',
    photoDisplay: ':unitId/images/:imageId/display-picture',
    communications: 'communications',
    communicationByUnitId: 'communications/unit/:unitId',
    communicationDeleteById: 'communications/soft-delete',
    categoryType: 'unit-type-categories',
    categoryByUnitType: 'types/list/:categoryId',
    categoryByMakeList: 'makes/category',
    makeBYModelList: 'models/make/:makeId',
    unitSpecifications: 'unit-specifications',
    update: 'update',
    unit: 'unit',
    unitImages: 'unit-images',
    unitDetails: 'unit-details',
    specification: 'specification',
    category: 'category',
    all: 'all',
    unitTypes: 'unit-type',
    unitTypesList: 'types/list',
    makes: 'makes',
    unitModel: 'unit-model',
    unitsAssociation: 'units-association',
    association: 'association',
    remove: 'remove',
    unitEventAuditTrails: 'unit-event-audit-trails',
    clone: 'clone',
    pdfGenerator: 'quotation/pdf/generator',
    internetOptions: 'internet-options',
    count: 'count',
  },
  financial: {
    root: 'financials',
    actualExpenses: ':financialId/actual-expenses',
    acquisitionMethod: 'acquisition-methods',
    purchasingAgent: 'users/financials/purchasing-agent',
    unitByFinancial: 'unit'
  },
  expenses: {
    root: 'expenses',
    vendorSupplier: 'vendors-suppliers/basic-info',
    expense_type: 'expense-types',
    expensesList: 'filter',
    attachments: 'expenses-attachments',
    acquisitionMethods: 'acquisition-methods'
  },
  pipeline: {
    root: 'pipeline',
    list: 'filter',
    dealer: 'dealer',
    pipelineDetail: 'pipeline-detail',
    phases: ':unitId/phases',
    status: ':pipelineId/status',
    pipelineowner: ':pipelineId/owner?',
    pipelineCommentList: 'pipeline-comments/pipeline'
  },
  pipelineComment: {
    root: 'pipeline-comments',
    pipeline: 'pipeline/:pipelineId',
  },
  fileUpload: {
    root: 'file/sync',
    delete: 'files',
    files: 'files/sync',
  },
  taskHistory: {
    root: 'task',
    list: ':taskId/history'
  },
  makes: {
    root: 'makes',
  },
  units: {
    root: 'units',
    type: 'types'
  },
  designations: {
    root: 'designations'
  },
  internetGroups: {
    root: 'internet-groups'
  },
  previousOwners: {
    root: 'previous-owners'
  },
  pipelineDetail: {
    root: 'pipeline-detail',
    unit: 'unitId/:unitId',
    pipelineDetailById: '/:id'
  },
  driverSchedule: {
    root: 'driver-schedules',
    address: 'general-information',
    status: 'options',
    statusList: ':driverScheduleId/status?',
    driverScheduleComment: 'driver-schedule',
    incomingAddress: ':unitId/address',
    send: 'send',
    email: 'email',
    driver: 'driver',
    pdf: 'pdf',
    generator: 'generator'
  },
  driverScheduleComment: {
    root: 'driver-schedule-comments',
    driverScheduleComments: ':driverScheduleId/comments'
  },
  incomingTruck: {
    root: 'incoming-trucks',
    status: 'status',
    incomingTruckComment: 'incoming-truck',
    fundingEmail: 'send/funding/email',
    generator: 'generator'
  },
  incomingTruckComment: {
    root: 'incoming-truck-comments',
    incomingTruckComments: ':incomingTruckId/comments'
  },
  saleUnit: {
    root: 'sell-units'
  },
  holdUnit: {
    root: 'on-hold-units'
  },
  dashboard: {
    root: 'dashboard',
    dailyMontlySales: 'daily-monthly-sales',
    truckInventory: 'truck-inventory',
    incomingTruckInventory: 'incoming-truck-inventory',
    taskNewAlert: 'task-new-alerts',
    pendingTask: 'pending-task',
    inventoryPrefernce: 'inventory-preference',
    inventoryAging: 'inventory-aging',
    yearlySales: 'yearly-sales'
  },
  firebase: {
    root: 'sfl/util/notification',
    recipient: '/recipient',
    register: '/register',
    unregister: '/unregister',
    history: '/history',
    read: '/read',
    count: '/count',
    notification: '/firebase-cloud-messaging-push-scope',
    delete: '/delete',
    notifications: 'notifications',
    filter: 'filter'
  },
  userAnnotation: {
    root: 'skeye-users',
    userFilter: 'quick-search/filter',
    delete: softDelete,
    archive: 'archive'
  },
  columnMasters: {
    root: 'column-masters',
    module: '/module',
    moduleWithoutSlash: 'module',
    preference: 'preference',
    activeTabName: 'activeTabName'
  },
  history: 'history/filter',
  filter: {
    root: 'filter-data',
    all: 'all',
    filterDataByUserIdAndModule: 'user/:userId/module',
    filterDataByFilterId: '/:filterId',
    defaultFilterByFilterId: '/:filterId/default',
    counts:'filter-counts'
  },
  specificationMasters: {
    root: 'specification-masters',
    latest: 'category'
  },
  subDomain: {
    root: 'sub-domain',
    dealer: 'dealer',
    details: 'details',
    abbreviation: 'abbreviation'
  },
  vendorsContactsSuppliers: "vendor-contact-supplier/all",
  calendar: {
    root: 'calendar',
    filter: 'filter/generic'

  },
  transportDriverBoard: "transport/driver-schedule-board",
  reports: {
    root: 'reporting',
    filter: 'filter',
    dailySales: 'sales',
    inventoryAging: 'inventory/ageing',
    inventoryProfit: 'inventory/profit',
    activityReport: 'crm-contact/activity',
    crmActivity: 'crm-contact/activity',
    agingEmail: 'aging-email-config',
    salesPersonListOfSalesReport: 'sales-persons',
    salesPersonListOfActivityReport: 'crm/account-reps',
  },
  fundingEmailConfig: {
    root: 'funding-email-config'
  }
};

export const APIS_TO_IGNORE_DUPLICATE_CHECK = [
  API_URL_UTIL.fileUpload.root,
  `${API_URL_UTIL.tasks.root}/${API_URL_UTIL.tasks.list}`,
  API_URL_UTIL.inventory.models, API_URL_UTIL.makes.root,
  API_URL_UTIL.admin.vendors.root,
  API_URL_UTIL.admin.crm.contact,
  API_URL_UTIL.inventory.clone,
  `${API_URL_UTIL.inventory.root}/${API_URL_UTIL.inventory.list}`,
  `${API_URL_UTIL.inventory.root}/${API_URL_UTIL.inventory.search}`,
  API_URL_UTIL.filter.root,
  API_URL_UTIL.admin.advertising.root,
  API_URL_UTIL.reports.agingEmail,
  API_URL_UTIL.admin.quoteFormConfig.root,
  `${API_URL_UTIL.subDomain.root}/${API_URL_UTIL.admin.quoteFormConfig.root}`,
  API_URL_UTIL.inventory.downloadDocuments,
  API_URL_UTIL.expenses.root,
  API_URL_UTIL.internetGroups.root
];
