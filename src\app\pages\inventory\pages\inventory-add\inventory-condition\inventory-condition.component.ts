import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Constants, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { InventoryComponentResponse, InventoryConditionsItem, InventoryListItem, PhotoType, UnitImages } from '@pages/inventory/models';
import { GeneralInfoService } from '@pages/inventory/services/general-info.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel, TableColumn } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';
import { ZoomImage } from 'src/app/shared/components/image-zoom-overlay/image-zoom-overlay.component';

@Component({
  selector: 'app-inventory-condition',
  templateUrl: './inventory-condition.component.html',
  styleUrls: ['./inventory-condition.component.scss']
})
export class InventoryConditionComponent extends BaseComponent implements OnInit, OnChanges {

  conditionFormGroup!: FormGroup;
  inventoryComponents: IdNameModel[] = [];
  conditionArrayInputs: InventoryConditionsItem[] = [];
  cols: TableColumn[] = [];
  _selectedColumns: any[] = [];
  unitId!: number;
  fileUploadProgresses: FileUploadProgress[] = [];
  inventoryConditionImages: UnitImages[] = [];
  taskFileUploadPath = 'Inventory Conditions Files';
  responsiveOptions!: any;
  isShowCarousel = false;
  zoomImages: ZoomImage[] = [];
  selectedPhotoIndex = 0;
  isConditionEmpty = false;
  @Input() isViewMode!: boolean;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() isEditMode = false;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onChangeActiveIndex: EventEmitter<boolean> = new EventEmitter<boolean>()


  constructor(private readonly inventoryService: InventoryService,
    private readonly fb: FormBuilder,
    private readonly toasterService: AppToasterService,
    private readonly fileUploadService: FileUploadService,
    private readonly authService: AuthService,
    private readonly generalInfoService: GeneralInfoService,
    private readonly cdf: ChangeDetectorRef,
    private readonly commonSharedService: CommonSharedService
  ) {
    super();
    this.responsiveOptions = [
      {
        breakpoint: '1024px',
        numVisible: 3,
        numScroll: 3
      },
      {
        breakpoint: '768px',
        numVisible: 2,
        numScroll: 2
      },
      {
        breakpoint: '560px',
        numVisible: 1,
        numScroll: 1
      }
    ];
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.setTableColumns();
    this.getComponentList();
    this.getUnitId();
    this.getCurrentUser();
    if (this.isEditMode && (this.inventoryInfo || this.inventoryIncomingInfo) || this.isViewMode) {
      this.getInventoryConditions();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      this.isEditMode = true;
    }
    if (!this.isViewMode) {
      this.conditionFormGroup?.enable();
    }
  }
  private initializeFormGroup(): void {
    this.conditionFormGroup = this.fb.group({
      inventoryCondition: this.fb.array([this.newinventoryConditionFormGroup])
    });
  }

  get newinventoryConditionFormGroup(): FormGroup {
    return this.fb.group({
      id: new FormControl(null),
      condition: new FormControl(null),
      unitComponentId: new FormControl(null),
      unitComponentName: new FormControl(null),
      unitId: new FormControl(null),
      unitImages: this.newUnitImagesFormArray,
    });
  }

  get inventoryConditionFormArray(): FormArray {
    return this.conditionFormGroup.get('inventoryCondition') as FormArray;
  }

  get newUnitImagesFormArray(): FormArray {
    return this.fb.array([]);
  }

  get unitImages(): FormGroup {
    return this.fb.group({
      photoType: new FormControl(null),
      unitId: new FormControl(null),
      url: new FormControl(null),
    })
  }

  get conditionsGroupFormArray(): InventoryConditionsItem[] {
    const unitId = this.unitId ? this.unitId : null;
    const params = []
    for (const inventoryCondition of this.inventoryConditionFormArray.value) {
      const unitImages = [];
      const conditionsUnitId = inventoryCondition.unitId ? inventoryCondition.unitId : unitId;
      for (const unitImage of inventoryCondition.unitImages) {
        const imageUnitId = unitImage.unitId ? unitImage.unitId : unitId;
        unitImages.push({
          photoType: unitImage.photoType,
          id: unitImage.id ? unitImage.id : null,
          unitId: this.isEditMode ? imageUnitId : unitId,
          url: unitImage.url,
          componentConditionId: inventoryCondition?.id
        });
      }
      const unitComponent = {
        id: inventoryCondition.id,
        condition: inventoryCondition.condition,
        unitComponentId: inventoryCondition.unitComponentId ? inventoryCondition.unitComponentId : null,
        unitId: this.isEditMode ? conditionsUnitId : unitId,
        unitImages: unitImages
      }
      params.push(unitComponent)
    }
    return params;
  }

  private setTableColumns() {
    this.cols = [
      { field: 'component', header: 'Component', class: 'small-col' },
      { field: 'condition', header: 'Condition', class: 'large-col' },
      { field: 'photos', header: 'Photos' },
    ];
    this._selectedColumns = this.cols;
  }

  private getUnitId() {
    this.inventoryService.getUnitId$().pipe(takeUntil(this.destroy$)).subscribe(res => {
      this.unitId = res;
    });
  }

  private getComponentList() {
    this.isLoading = true;
    this.inventoryConditionFormArray.clear();
    this.inventoryService.get<IdNameModel[]>(API_URL_UTIL.inventory.components).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.isLoading = false;
        this.inventoryComponents = res;
        this.inventoryComponents.forEach(component => {
          this.inventoryConditionFormArray.push(this.fb.group({
            id: null,
            condition: null,
            unitComponentId: component.id,
            unitComponentName: component.name,
            unitId: null,
            unitImages: this.newUnitImagesFormArray
          }))
        });
      },
      error: () => {
        this.isLoading = false;
      }
    })
  }

  private uploadImage(file: File, index: number): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
          this.unitImagesFormArrayByIndex(index).push(this.fb.group({
            photoType: PhotoType.COMPONENT_CONDITION,
            unitId: null,
            id: null,
            url: fileUrl.url,
            fileUrl: uploadProgress.fileProperty?.fileUrl
          }));
          this.cdf.detectChanges();
        },
        error: () => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  private setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
      }
    });
  }

  private saveInventoryConditions(close = false): void {

    this.generalInfoService.add(this.conditionsGroupFormArray, API_URL_UTIL.inventory.componentConditions).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(this.isEditMode ? MESSAGES.conditionAddSuccess : MESSAGES.conditionUpdateSuccess);
        if (close) {
          this.commonSharedService.setBlockUI$(false);
        }
        else {
          this.onChangeActiveIndex.next(true);
          this.commonSharedService.setBlockUI$(false);
        }
      }
    });
  }

  private editInventoryConditions(close = false): void {
    this.generalInfoService.update(this.conditionsGroupFormArray, API_URL_UTIL.inventory.componentConditions).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(this.isEditMode ? MESSAGES.conditionUpdateSuccess : MESSAGES.conditionAddSuccess);
        if (!close) {
          this.onChangeActiveIndex.next(true);
        }
        this.commonSharedService.setBlockUI$(false);
      }
    });
  }

  private getInventoryConditions(): void {
    const unitId = this.inventoryIncomingInfo ? this.inventoryIncomingInfo?.unitId : this.inventoryInfo?.id;
    const endpoint = API_URL_UTIL.inventory.componentsDetails.replace(':unitId', String(unitId));
    this.inventoryService.get<InventoryComponentResponse[]>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (components) => {
        this.isConditionEmpty = components.length ? false : true;
        for (const component of components) {
          for (const [index, controls] of this.inventoryConditionFormArray.controls.entries()) {
            if (controls.get('unitComponentId')?.value === component.unitComponent.id) {
              controls.get('id')?.patchValue(component.id);
              controls.get('condition')?.patchValue(component.condition);
              controls.get('unitComponentName')?.patchValue(component.unitComponent.name);
              controls.get('unitComponentId')?.patchValue(component.unitComponent.id);
              controls.get('unitId')?.patchValue(component.unitId);
              if (component.unitImages.length) {
                for (const unitImage of component.unitImages) {
                  this.unitImagesFormArrayByIndex(index).push(this.fb.group({
                    photoType: unitImage.photoType,
                    id: unitImage.id,
                    unitId: unitImage.unitId,
                    url: unitImage.url,
                    fileUrl: unitImage.fullUrl
                  }));
                }
              }
            }
          }
        }
        if (this.isViewMode) {
          this.conditionFormGroup.disable();
        }
      }
    })
  }

  unitImagesFormArrayByIndex(index: number): FormArray {
    return this.inventoryConditionFormArray.at(index).get('unitImages') as FormArray;
  }

  openCarousel(index: number) {
    this.inventoryConditionImages = [];
    this.inventoryConditionImages = this.inventoryConditionFormArray.at(index).get('unitImages')?.value;

    if (this.inventoryConditionImages.length) {
      this.zoomImages = this.inventoryConditionImages.map((image, idx) => ({
        fullUrl: image.fileUrl,
        alt: `Condition Image ${idx + 1}`
      }));

      this.selectedPhotoIndex = 0;
      this.isShowCarousel = true;
    }
  }

  closeZoomOverlay(): void {
    this.isShowCarousel = false;
  }

  onImageIndexChange(index: number): void {
    this.selectedPhotoIndex = index;
  }

  onFileSelect(event: any, index: number, rowData: object) {
    if (event.target?.files?.length) {
      for (const file of event.target.files) {
        if (file.size > this.constants.fileSize) {
          this.toasterService.warning(MESSAGES.fileUploadMessage);
          return;
        }
        const extensionDot = file?.name?.lastIndexOf('.');
        const ext = file?.name?.substring(extensionDot + 1);
        if (!Constants.allowedImgFormats.includes(ext)) {
          this.toasterService.error(MESSAGES.fileTypeNotSupported);
          return;
        }
        this.uploadImage(file, index);
      }
    }
  }

  get isFileUploadInProgress(): boolean {
    if (this.fileUploadProgresses.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  onSubmit(close = false, unitId = 0): void {
    if (this.isViewMode) {
      this.conditionFormGroup.enable();
      this.isViewMode = false;
      return;
    }
    if (this.isFileUploadInProgress) {
      this.toasterService.warning(MESSAGES.fileUploadInProgress);
      return;
    }
    this.commonSharedService.setBlockUI$(true);
    if (this.isEditMode) {
      if (this.isConditionEmpty) {
        this.saveInventoryConditions(close);
      } else {
        this.editInventoryConditions(close);
      }
    }
    else {
      this.saveInventoryConditions(close);
    }
  }

  onSubmitAndNext(close = false): void {
    this.onSubmit(close);
  }
}
