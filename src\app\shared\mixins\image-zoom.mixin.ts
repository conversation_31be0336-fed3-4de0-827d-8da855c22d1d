import { ZoomImage } from '../components/image-zoom-overlay/image-zoom-overlay.component';
import { ImageZoomService } from '../services/image-zoom.service';

export interface ImageZoomMixin {
  zoomImages: ZoomImage[];
  selectedPhotoIndex: number;
  isShowCarousel: boolean;
  imageZoomService: ImageZoomService;

  closeZoomOverlay(): void;
  onImageIndexChange(index: number): void;
}

export class ImageZoomMixinImpl implements ImageZoomMixin {
  zoomImages: ZoomImage[] = [];
  selectedPhotoIndex = 0;
  isShowCarousel = false;

  constructor(public imageZoomService: ImageZoomService) {}

  closeZoomOverlay(): void {
    this.isShowCarousel = false;
  }

  onImageIndexChange(index: number): void {
    this.selectedPhotoIndex = index;
  }

  openTaskAttachmentZoom(attachments: any[], clickedUrl: string): void {
    if (clickedUrl && attachments?.length) {
      this.selectedPhotoIndex = this.imageZoomService.findTaskAttachmentIndex(attachments, clickedUrl);
      this.zoomImages = this.imageZoomService.prepareTaskAttachmentZoomImages(attachments);
      this.isShowCarousel = true;
    }
  }

  openInventoryPhotoZoom(photos: any[], clickedUrl: string): void {
    if (clickedUrl && photos?.length) {
      this.selectedPhotoIndex = this.imageZoomService.findImageIndex(photos, clickedUrl, 'fileUrl');
      this.zoomImages = this.imageZoomService.prepareInventoryPhotoZoomImages(photos);
      this.isShowCarousel = true;
    }
  }

  openUnitImageZoom(images: any[], clickedUrl: string): void {
    if (clickedUrl && images?.length) {
      this.selectedPhotoIndex = this.imageZoomService.findImageIndex(images, clickedUrl, 'fileUrl');
      this.zoomImages = this.imageZoomService.prepareUnitImageZoomImages(images);
      this.isShowCarousel = true;
    }
  }

  openCombinedPhotoZoom(photoArrays: any[][], clickedUrl: string): void {
    const combinedPhotos = this.imageZoomService.combinePhotoArrays(...photoArrays);
    if (clickedUrl && combinedPhotos?.length) {
      this.selectedPhotoIndex = this.imageZoomService.findImageIndex(combinedPhotos, clickedUrl, 'fileUrl');
      this.zoomImages = this.imageZoomService.prepareInventoryPhotoZoomImages(combinedPhotos);
      this.isShowCarousel = true;
    }
  }
}

export function createImageZoomMixin(imageZoomService: ImageZoomService): ImageZoomMixinImpl {
  return new ImageZoomMixinImpl(imageZoomService);
}

export function WithImageZoom<T extends { new (...args: any[]): {} }>(constructor: T) {
  return class extends constructor implements ImageZoomMixin {
    zoomImages: ZoomImage[] = [];
    selectedPhotoIndex = 0;
    isShowCarousel = false;
    imageZoomService!: ImageZoomService;

    closeZoomOverlay(): void {
      this.isShowCarousel = false;
    }

    onImageIndexChange(index: number): void {
      this.selectedPhotoIndex = index;
    }
  };
}
