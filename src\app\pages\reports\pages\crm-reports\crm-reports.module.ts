import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { TableModule } from 'primeng/table';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { PipesModule } from "../../../../@shared/pipes/pipes.module";
import { CrmReportsRoutingModule } from './crm-reports-routing.module';
import { CrmReportsComponent } from './crm-reports.component';
import { ActivityReportComponent } from './pages/activity-report/activity-report.component';
import { DailySalesReportComponent } from './pages/daily-sales-report/daily-sales-report.component';
import { InventorySalesReportComponent } from './pages/inventory-sales-report/inventory-sales-report.component';


@NgModule({
  declarations: [
    CrmReportsComponent,
    DailySalesReportComponent,
    ActivityReportComponent,
    InventorySalesReportComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CrmReportsRoutingModule,
    SharedComponentsModule,
    TableModule,
    DropdownModule,
    MultiSelectModule,
    FontAwesomeIconsModule,
    CalendarModule,
    DirectivesModule,
    PipesModule
]
})
export class CrmReportsModule { }
