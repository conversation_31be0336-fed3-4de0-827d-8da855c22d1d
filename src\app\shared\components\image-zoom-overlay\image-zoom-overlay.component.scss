.image-zoom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(5px);
}

.zoom-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  z-index: 10001;
}

.zoom-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.help-btn {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 20px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  z-index: 10001;
}

.help-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.image-counter {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10001;
}

.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  z-index: 10001;
}

.nav-arrow:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-arrow-left {
  left: 20px;
}

.nav-arrow-right {
  right: 20px;
}

.nav-arrow-right fa-icon {
  transform: rotate(-90deg); /* Rotate arrow down to point right */
}

.zoom-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 25px;
  z-index: 10001;
}

.zoom-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.zoom-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.zoom-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.zoom-btn.active {
  background: rgba(255, 255, 255, 0.4);
  color: #007bff;
}

.zoom-level {
  color: white;
  font-size: 14px;
  min-width: 50px;
  text-align: center;
}

.zoom-image-container {
  position: relative;
  width: 90vw;
  height: 90vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  // TODO: Enable cursor styling if needed in the future
  // cursor: zoom-in;
}

.zoom-image-container.can-pan {
  cursor: grab;
}

.zoom-image-container.can-pan:active {
  cursor: grabbing;
}

.zoom-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transform-origin: center center;
  user-select: none;
  -webkit-user-drag: none;
}

// Help Overlay Styles
.help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10002;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(3px);
}

.help-content {
  background: rgba(40, 40, 40, 0.95);
  border-radius: 15px;
  padding: 30px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 15px;

  h3 {
    margin: 0;
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
  }
}

.help-close {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 18px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

.help-sections {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.help-section {
  h4 {
    margin: 0 0 15px 0;
    color: #4fc3f7;
    font-size: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  ul {
    margin: 0;
    padding-left: 20px;
    list-style: none;

    li {
      margin-bottom: 10px;
      line-height: 1.5;
      position: relative;
      padding-left: 15px;

      &:before {
        content: "•";
        color: #4fc3f7;
        position: absolute;
        left: 0;
        font-weight: bold;
      }

      strong {
        color: #81c784;
        font-weight: 600;
      }
    }
  }
}

// Feature Hints Styles
.feature-hints {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10001;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
}

.hint {
  background: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  animation: hintSlideIn 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(3px);
  transition: all 0.3s ease;
}

@keyframes hintSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@media (max-width: 768px) {
  .zoom-close-btn {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .image-counter {
    top: 10px;
    font-size: 12px;
    padding: 6px 12px;
  }

  .nav-arrow {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .nav-arrow-left {
    left: 10px;
  }

  .nav-arrow-right {
    right: 10px;
  }
  
  .nav-arrow-right fa-icon {
    transform: rotate(-90deg); /* Ensure rotation works on mobile too */
  }

  .zoom-controls {
    bottom: 20px;
    padding: 8px 16px;
    gap: 8px;
  }

  .zoom-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .zoom-level {
    font-size: 12px;
    min-width: 40px;
  }

  .zoom-image-container {
    width: 95vw;
    height: 85vh;
  }

  .help-btn {
    top: 10px;
    left: 10px;
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .help-content {
    margin: 20px;
    padding: 20px;
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 40px);

    .help-header h3 {
      font-size: 20px;
    }

    .help-section h4 {
      font-size: 16px;
    }

    .help-section ul li {
      font-size: 14px;
      margin-bottom: 8px;
    }
  }

  .feature-hints {
    top: 60px;
    left: 10px;
    right: 10px;
    transform: none;

    .hint {
      font-size: 12px;
      padding: 10px 16px;
    }
  }
}
