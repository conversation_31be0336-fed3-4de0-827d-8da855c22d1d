import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Constants, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { VendorListItem } from '@pages/administration/models';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ExpensesAttachment, ExpensesCreateParams, ExpensesListItem, FinancialInformation, InventoryListItem, ModelType, VendorSupplierBasicInfo } from '@pages/inventory/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { TaskService } from '@pages/shops/services/tasks.service';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { FileProperties, FileUploadProgress } from 'src/app/@shared/models/file-upload.model';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-expenses-add',
  templateUrl: './expenses-add.component.html',
  styleUrls: ['./expenses-add.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class ExpensesAddComponent extends BaseComponent implements OnInit, OnChanges {
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  title = 'Add Expenses';
  ModelType = ModelType;
  displaySelectionDialog!: boolean;
  expensesFormGroup!: FormGroup;
  isEditMode = false;
  expensesType: IdNameModel[] = [];
  vendorSupplierList: VendorSupplierBasicInfo[] = [];
  hasDataBeenModified = false
  loaders = {
    expensesType: false,
    vendors: false
  };
  @Input() isViewMode!: boolean | null;
  @Input() expensesInfo!: ExpensesListItem | null;
  selectedVendor!: VendorListItem | null;
  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'Expenses Files';
  currentUser!: Account | null;
  modelPopups = {
    showCreateVendor: false,
    showCreateSupplier: false,
  };
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() financialInformation!: FinancialInformation;
  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly authService: AuthService,
    private readonly taskService: TaskService,
    private readonly confirmationService: ConfirmationService,
    private readonly fileUploadService: FileUploadService,
    private readonly cdf: ChangeDetectorRef,
    private readonly expensesService: ExpensesService,
    private readonly inventoryService: InventoryService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getExpensesTypeList();
    this.getVendorSupplierList();
    this.getCurrentUser()
    if (this.isEditMode) {
      this.setExpensesFormGroup();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.expensesInfo.currentValue) {
      this.title = 'Edit Expense';
      this.isEditMode = true;
    }
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user && this.expensesFormGroup) {
        this.currentUser = user;
      }
    });
  }

  private initializeFormGroup(): void {
    this.expensesFormGroup = this.formBuilder.group({
      poRoNumber: new FormControl(null),
      invoiceNumber: new FormControl(null),
      description: new FormControl(null),
      vendorId: new FormControl(null),
      supplierId: new FormControl(null),
      amount: new FormControl(null, [Validators.required]),
      transDate: new FormControl(null, [Validators.required]),
      expenseTypeId: new FormControl(null, [Validators.required]),
      unitId: new FormControl(null),
      firstExpense: new FormControl(false),
      contactAndVendorAndSupplierType: new FormControl('VENDOR'),
    });
  }

  private async setExpensesFormGroup(): Promise<void> {
    if (this.expensesInfo) {
      this.expensesFormGroup.patchValue({
        poRoNumber: this.expensesInfo.poRoNumber,
        invoiceNumber: this.expensesInfo.invoiceNumber,
        description: this.expensesInfo.description,
        amount: this.expensesInfo.amount,
        transDate: this.expensesInfo?.transDate ? new Date(`${this.expensesInfo?.transDate}`) : '',
        expenseTypeId: this.expensesInfo.expenseType.id,
        contactAndVendorAndSupplierType: this.expensesInfo.contactAndVendorAndSupplierType
      });
      switch (this.expensesInfo.contactAndVendorAndSupplierType) {
        case ModelType.VENDOR.toUpperCase():
          if (this.expensesInfo.vendor?.id) {
            this.expensesFormGroup.patchValue({
              vendorId: `${this.expensesInfo.vendor.id}-${ModelType.VENDOR.toUpperCase()}`
            });
          } else {
            this.expensesFormGroup.patchValue({
              vendorId: null
            });
          }
          break;
        case ModelType.SUPPLIER.toUpperCase():
          this.expensesFormGroup.patchValue({
            vendorId: `${this.expensesInfo.supplier.id}-${ModelType.SUPPLIER.toUpperCase()}`
          });
          break;
        default:
          break;
      }
    }
    this.cdf.detectChanges();
  }

  setVendorSupplier(event: any): void {
    const selectedText = event.originalEvent.srcElement.innerText;
    const type = selectedText && selectedText.split('(')[1].split(')')[0].trim();
    switch (type) {
      case ModelType.VENDOR.toUpperCase():
        this.expensesFormGroup.controls['contactAndVendorAndSupplierType'].setValue(ModelType.VENDOR.toUpperCase());
        break;
      case ModelType.SUPPLIER.toUpperCase():
        this.expensesFormGroup.controls['contactAndVendorAndSupplierType'].setValue(ModelType.SUPPLIER.toUpperCase())
        break;
      default:
        break;
    }
  }

  get expensesAttachments(): ExpensesAttachment[] {
    let expensesAttachments: ExpensesAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl }));
    if (this.isEditMode && this.expensesInfo?.expensesAttachments?.length) {
      expensesAttachments = [...this.expensesInfo.expensesAttachments, ...expensesAttachments];
    }
    return expensesAttachments;
  }

  get expensesInfoCreateParams(): ExpensesCreateParams {
    const rawVendorIdValue = this.expensesFormGroup.controls['vendorId'].value;
    let typeId: null | number = null;
    if (rawVendorIdValue) {
      typeId = Number(rawVendorIdValue.split('-')[0]);
    }
    const contactAndVendorAndSupplierType = this.expensesFormGroup.controls['contactAndVendorAndSupplierType'].value;
    const getFormValues = () => {
      const commonValues = {
        ...this.expensesFormGroup.getRawValue()
      };
      if (contactAndVendorAndSupplierType === ModelType.VENDOR.toUpperCase()) {
        return { ...commonValues, vendorId: typeId, supplierId: null }
      } else {
        return { ...commonValues, vendorId: null, supplierId: typeId }
      }
    };
    return {
      ...getFormValues(),
      financialId: this.financialInformation.id,
      id: this.expensesInfo?.id,
      expensesAttachments: this.expensesAttachments,
      unitId: this.financialInformation?.unitId,
      firstExpense: false,

    };
  }

  get isFileUploadInProgress(): boolean {
    if (this.fileUploadProgresses.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  onSubmit(close = true): void {
    if (this.expensesFormGroup.invalid) {
      this.expensesFormGroup.markAllAsTouched();
      return;
    }
    if (this.isFileUploadInProgress) {
      this.toasterService.warning(MESSAGES.fileUploadInProgress);
      return;
    }
    if (this.isEditMode) {
      this.editExpenses();
    } else {
      this.saveExpenses(close);
    }

  }

  saveExpenses(close = true): void {
    this.expensesService.add(this.expensesInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.expensesUpdateSuccess : MESSAGES.expensesAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.expensesFormGroup.reset();
      }
    });
  }

  editExpenses(): void {
    this.expensesService.update(this.expensesInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.expensesUpdateSuccess : MESSAGES.expensesAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
  }

  private getExpensesTypeList(): void {
    this.loaders.expensesType = true;
    this.expensesService.getExpensesList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (purchasingAgent) => {
        this.expensesType = purchasingAgent;
        this.loaders.expensesType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.expensesType = false;
      }
    });
  }

  private getVendorSupplierList(): void {
    this.loaders.vendors = true;
    this.expensesService.getVendorSupplierList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (vendorSupplierList) => {
        vendorSupplierList.forEach((d: any) => {
          d.name = `${d.name} (${d.type})`;
          d.id = `${d.id}-${d.type}`;
          return d
        })
        this.vendorSupplierList = vendorSupplierList;
        this.loaders.vendors = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.vendors = false;
      }
    });
  }

  openModel(type: string): void {
    switch (type) {
      case ModelType.VENDOR:
        this.modelPopups.showCreateVendor = true;
        break;
      case ModelType.SUPPLIER:
        this.modelPopups.showCreateSupplier = true;
        break;
      default:
        break;
    }
  }

  showDialog(): void {
    this.displaySelectionDialog = true;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.modelPopups.showCreateVendor = false
    this.modelPopups.showCreateSupplier = false
    this.selectedVendor = null;
    this.displaySelectionDialog = false;
    if (refreshList) {
      this.getVendorSupplierList();
    }
  }

  onFileSelect(event: any): void {
    if (event.target?.files?.length) {
      for (const file of event.target.files) {
        if (file.size > this.constants.fileSize) {
          this.toasterService.warning(MESSAGES.fileUploadMessage)
          return;
        }
        const modifiedFileName = getRefactorFileName(file.name);
        const isExtensionSupported = allowExtensions(modifiedFileName, Constants.allowedPdfFormats)
        if (isExtensionSupported) {
          const modifiedFile = new File([file], modifiedFileName, { type: file.type });
          this.uploadImage(modifiedFile);
        } else {
          this.toasterService.error(MESSAGES.fileTypeSupportedPDF);
          return;
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  downloadImage(taskAttachment: ExpensesAttachment): void {
    if (taskAttachment?.url) {
      this.fileUploadService.downloadFile(taskAttachment.url, this.getFileName(taskAttachment.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), this.getFileName(taskAttachment.url));
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex: number, callbackFn: Function): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      callbackFn(fileIndex);
    })
  }

  removeFileFromUpload(fileIndex: number): void {
    const spliceImageFunction = () => {
      this.fileUploadProgresses.splice(fileIndex, 1);
      this.cdf.detectChanges();
    }
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl, fileIndex, spliceImageFunction);
    }
  }

  onDelete(attachmentId: number | undefined, event: Event): void {
    if (!attachmentId) {
      return;
    }
    this.confirmationService.confirm({
      header: 'Confirmation',
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'attachment'),
      icon: icons.triangle,
      accept: () => {
        this.onDeleteConfirmation(attachmentId);
      }
    });
  }

  onDeleteConfirmation(attachmentId: number): void {
    this.expensesService.deleteExpenseAttachment(attachmentId).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.attachmentDeleteSuccess);
      const index = this.expensesInfo?.expensesAttachments?.findIndex(attachment => attachment.id === attachmentId);
      if (index !== undefined && index > -1) {
        this.expensesInfo?.expensesAttachments?.splice(index, 1);
      }
      this.cdf.detectChanges();
    });
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');
  }
}
