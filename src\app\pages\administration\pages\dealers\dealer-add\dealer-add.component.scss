@import '/src/assets/scss/theme/mixins';

.add-shop {
  width: 200px;

  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.add-shops-btn {
  @include flex-end;
  margin-bottom: 5px;
}

.map-icon {
  margin-top: 23px;
  button {
    display: flex;
    flex-direction: row-reverse;
    height: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    scroll-margin-bottom: 100px;

    img {
      margin-right: 10px;
    }
  }
}

.shop-group {
  margin-bottom: 15px;
}

.delete-shop {
  &:hover {
    cursor: pointer;
  }
}

.pac-container {
  z-index: 9999999 !important;
}

@media only screen and (max-width: 500px) {
  .dealer-title {
    display: block !important;
    justify-content: space-between !important;
  }

  .dealer-form .content {
    padding-bottom: 50px;
  }
}

.dealer-title {
  display: flex;
  justify-content: space-between;
}

.f-s-12 {
  font-size: 12px;
}

.header-title {
  top: -8px;
  position: relative;
}
.created-by {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 400;
  position: absolute;
  top: 30px;

  .dealer-header-info {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }

  .dealer-logo-header {
    display: flex;
    align-items: center;
    gap: 10px;

    .header-logo {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 4px;
      border: 1px solid #ddd;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }

    .dealer-name-small {
      font-size: 14px;
      color: var(--text-color);
      font-weight: 500;
    }
  }

  .created-info {
    margin-top: 5px;
  }
}
.bold-text {
  font-size: 16px;
  color: var(--text-color);
  margin-right: 5px;
}

.public-url {
  text-transform: none !important;
  a {
    text-transform: none !important;
  }
}
.email-col {
  width: 300px;
}
.dealer-details-container {

  .dealer-logo {
    .drop-zone {
      height: 150px !important;
    }
  }

  .uploaded-logo-display {
    .logo-preview-container {
      position: relative;
      display: inline-block;
    }

    .logo-preview {
      position: relative;
      display: inline-block;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);

        .logo-actions {
          opacity: 1;
        }
      }

      .dealer-logo-image {
        width: 200px;
        height: auto;
        max-height: 150px;
        object-fit: cover;
        display: block;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }

      .logo-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;

        ::ng-deep .p-progressbar {
          width: 100%;
          height: 6px;

          .p-progressbar-value {
            background: var(--primary-color);
          }
        }
      }

      .logo-actions {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;

        .action-icon {
          width: 32px;
          height: 32px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 14px;
          color: #495057;
          transition: all 0.2s ease;
          backdrop-filter: blur(4px);

          &:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
          }

          &.delete-icon {
            color: #dc3545;

            &:hover {
              background: #dc3545;
              color: white;
            }
          }
        }
      }
    }

    .add-new-logo {
      margin-top: 15px;
      text-align: center;

      .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    flex-direction: column;

    .dealer-logo {
      margin-bottom: 1rem;
    }

    .uploaded-logo-display {
      display: flex;
      justify-content: center;
    }

    .dealer-details {
      --bs-gutter-x: 0;
    }
  }
}
