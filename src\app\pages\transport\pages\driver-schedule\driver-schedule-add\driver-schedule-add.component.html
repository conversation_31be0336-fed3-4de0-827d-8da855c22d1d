<div class="modal-title" [ngClass]="isEditMode ? 'edit-header' : ''">
  <h4 [ngClass]="(isViewMode || isEditMode) ? 'header-title' : ''">{{ isViewMode ? 'View Driver Schedule' : title }}</h4>
  <span *ngIf="isEditMode || isViewMode" class="created-by">
    <span class="bold-text">#{{ driverScheduleInfo?.item }}</span>
    Created By <span class="bold-text">{{ driverScheduleInfo?.createdBy?.name }}</span> on {{ driverScheduleInfo?.createdDate | date : constants.monthDateAndYearFormat }} at
    {{ driverScheduleInfo?.createdDate | date : constants.time }}
  </span>
  <div>
    <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
  </div>
</div>

<form (ngSubmit)="onSubmit()" [formGroup]="driverScheduleFormGroup" class="driver-schedule-add">
  <div class="content">
    <button class="btn btn-primary dummy-data-btn hide-populate-data" type="button" *ngIf="environment.showButtonToAddDummyData && !isEditMode">Populate User Data</button>
    <section>
      <div class="card p-3">
        <div class="title">
          <h4>SCHEDULER</h4>
        </div>
        <div class="row m-t-10">
          <div class="schedule-content col-lg-3 col-md-6 col-12">
            <div class="d-flex">
              <span *ngFor="let schedule of scheduleFor" class="stock-title-rb">
                <p-radioButton
                  *ngIf="schedule.name"
                  name="scheduleFor"
                  [inputId]="schedule.name"
                  [value]="schedule.name"
                  formControlName="scheduleFor"
                  (onClick)="onChange(schedule.name)"
                  [label]="schedule.name"
                >
                </p-radioButton>
              </span>
            </div>
            <div>
              <div *ngIf="driverScheduleFormGroup.controls?.scheduleFor?.value === scheduleForList?.stock">
                <p-dropdown
                  appPreventClearFilter
                  [options]="stockList"
                  formControlName="stockNumber"
                  [filter]="true"
                  (onFilter)="searchStock($event)"
                  optionLabel="stockNumber"
                  optionValue="id"
                  appendTo="body"
                  placeholder="Select stock"
                  (onChange)="selectedStockNumberByAddress($event.value)"
                  [virtualScroll]="true"
                  [itemSize]="30"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.stockNumbers, data: stockList }"></ng-container>
                  </ng-template>
                  <ng-template pTemplate="selectedItem">
                    <div *ngIf="driverScheduleFormGroup.controls.stockNumber?.value" class="driver-schedule">
                      <ng-container *ngFor="let stock of stockList">
                        <ng-container *ngIf="stock.id === driverScheduleFormGroup.controls.stockNumber?.value">
                          {{ stock.stockNumber }}
                          <span *ngFor="let unit of stock?.unitAssociationChildDTO"> , {{ unit.stockNumber }} </span>
                        </ng-container>
                      </ng-container>
                    </div>
                  </ng-template>
                  <ng-template let-stock pTemplate="item">
                    {{ stock.stockNumber }}
                    <span *ngFor="let unit of stock?.unitAssociationChildDTO"> , {{ unit.stockNumber }} </span>
                  </ng-template>
                  <ng-template pTemplate="footer">
                    <p *ngIf="!isLastPage" class="load-more" (click)="onLoadMore()">Load More <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loadMoreIcon"></fa-icon></p>
                  </ng-template>
                </p-dropdown>

                <app-error-messages [control]="driverScheduleFormGroup.controls.stockNumber"></app-error-messages>
              </div>
              <div *ngIf="driverScheduleFormGroup.controls?.scheduleFor?.value === scheduleForList?.title">
                <input class="form-control" type="text" placeholder="Enter title" formControlName="title" />
                <app-error-messages [control]="driverScheduleFormGroup.controls.title"></app-error-messages>
              </div>
              <div><span class="text-danger f-s-12" *ngIf="isRequired">please select stock number or title</span></div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label class="required">Requested By</label>
            <input class="form-control" type="text" placeholder="Created By" formControlName="createdByName" />
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label class="required">Status</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="statues"
              formControlName="driverScheduleStatus"
              optionLabel="name"
              optionValue="id"
              filterBy="name"
              [disabled]="this.currentRole === 'ROLE_USER' ? true : false"
              [showClear]="true"
              [filter]="true"
              placeholder="Select status"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.status, data: statues }"></ng-container>
              </ng-template>
              <ng-template let-status pTemplate="item">
                <span>{{ status?.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="driverScheduleFormGroup.controls.driverScheduleStatus"></app-error-messages>
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label class="required">Dispatcher</label>
            <p-dropdown
              appPreventClearFilter
              appendTo="body"
              [options]="dispatcherNameList"
              formControlName="dispatcherId"
              optionLabel="name"
              optionValue="id"
              filterBy="name"
              [showClear]="true"
              [filter]="true"
              placeholder="Select Dispatcher"
            >
              <ng-template pTemplate="empty">
                <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.status, data: dispatcherNameList }"></ng-container>
              </ng-template>
              <ng-template let-status pTemplate="item">
                <span>{{ status?.name }}</span>
              </ng-template>
            </p-dropdown>
            <app-error-messages [control]="driverScheduleFormGroup.controls.dispatcherId"></app-error-messages>
          </div>

          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label>Deadline</label>
            <div>
              <p-calendar
                appendTo="body"
                formControlName="endDate"
                [showIcon]="true"
                [showButtonBar]="true"
                [readonlyInput]="true"
                inputId="endDateIcon"
                (onSelect)="onEndDateChange($event)"
                (onClearClick)="clearDate()"
                [minDate]="today"
                [disabled]="this.currentRole === 'ROLE_DRIVER' ? true : false"
              >
              </p-calendar>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label>Day of Delivery</label>
            <input class="form-control" type="text" placeholder="Enter Day of Delivery" formControlName="weekDay" [readOnly]="true" />
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label>Vin</label>
            <input class="form-control" type="text" placeholder="Enter Vin" formControlName="vin" />
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label>Make</label>
            <input class="form-control" type="text" placeholder="Enter Make" formControlName="make" />
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label>Model</label>
            <input class="form-control" type="text" placeholder="Enter Model" formControlName="model" />
          </div>
          <div class="col-lg-3 col-md-6 col-12 pb-8">
            <label>Year</label>
            <input class="form-control" type="text" placeholder="Enter Year" formControlName="year" />
          </div>
        </div>
      </div>
      <div class="card p-3 mt-3">
        <ng-container *ngIf="scheduleVisiblity">
          <div class="title m-t-20">
            <h4>DRIVER SCHEDULER</h4>
          </div>
          <div class="row m-t-10">
            <div class="col-lg-3 col-md-6 col-12">
              <div class="d-flex space-between">
                <div>
                  <label class="required">Driver</label>
                </div>
                <div class="d-flex justify-content-end btn-driver" *ngIf="!isViewMode">
                  <button
                    class="add-driver-btn btn btn-primary"
                    type="button"
                    (click)="onAdd()"
                    *appHasPermission="[permissionActions.CREATE_USERS]"
                    [appImageIconSrc]="constants.staticImages.icons.add"
                    (click)="onAdd()"
                  ></button>
                </div>
              </div>
              <div class="multi-select-dropdown">
              <p-multiSelect
                appPreventClearFilter
                [options]="driverNameList"
                defaultLabel="Select a Driver"
                formControlName="driverId"
                optionLabel="name"
                optionValue="id"
                [maxSelectedLabels]="1"
                [showToggleAll]="false"
                optionDisabled="disabled"
                (onChange)="updateDisabledStatusForDrivers()"
                selectedItemsLabel="{0} items selected"
                [(ngModel)]="driverIds"
                appendTo="body"
                [disabled]="this.currentRole === 'ROLE_DRIVER' ? true : false"
              >
                <ng-template pTemplate="empty">
                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.role, data: driverNameList }"></ng-container>
                </ng-template>
                <ng-template let-stock pTemplate="item">
                  <span>{{ stock.name }}</span>
                </ng-template>
              </p-multiSelect>
              <app-error-messages [control]="driverScheduleFormGroup.controls.driverId"></app-error-messages>
              <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="driverIds?.length" (click)="clearDrivers()"></fa-icon>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </section>
    <section formGroupName="pickUpLocation">
      <div class="card p-3 mt-3">
        <div class="title">
          <h4>WHERE IS THE UNIT LOCATED?</h4>
        </div>
        <div class="row">
          <div class="col-md-6 col-12">
            <label class="required">Address</label>
            <input
              class="form-control"
              type="text"
              ngx-google-places-autocomplete
              ngx-gp-autocomplete
              [options]="options"
              (onAddressChange)="handleAddressChange($event)"
              placeholder="Enter address"
              formControlName="streetAddress"
            />
            <app-error-messages [control]="pickUpAddressFormGroup.controls.streetAddress"></app-error-messages>
          </div>

          <div class="col-lg-2 col-md-6 col-12">
            <label class="required">City</label>
            <input class="form-control" type="text" placeholder="Enter city name" formControlName="city" />
            <app-error-messages [control]="pickUpAddressFormGroup.controls.city"></app-error-messages>
          </div>
          <div class="col-lg-2 col-md-6 col-12">
            <label class="required">State</label>
            <input class="form-control" type="text" placeholder="Enter state name" formControlName="state" />
            <app-error-messages [control]="pickUpAddressFormGroup.controls.state"></app-error-messages>
          </div>
          <div class="col-lg-2 col-md-5 col-10">
            <label class="required">Zip code</label>
            <input class="form-control" type="text" placeholder="Enter zip code" formControlName="zipcode" />
            <app-error-messages [control]="pickUpAddressFormGroup.controls.zipcode"></app-error-messages>
          </div>
          <div class="col-1">
            <div
              class="add-shop map-icon"
              *ngIf="
                pickUpAddressFormGroup.get('streetAddress')?.value ||
                pickUpAddressFormGroup.get('state')?.value ||
                pickUpAddressFormGroup.get('city')?.value ||
                pickUpAddressFormGroup.get('zipcode')?.value
              "
            >
              <button class="btn btn-primary" type="button" (click)="toggleGoogleMapPopUp()">
                <fa-icon [icon]="faIcons.faLocationDot"></fa-icon>
              </button>
            </div>

            <p-sidebar
              [closeOnEscape]="false"
              [dismissible]="false"
              [(visible)]="showGoogleMapSideBar"
              position="right"
              (onHide)="showGoogleMapSideBar = false"
              [blockScroll]="true"
              [showCloseIcon]="false"
              styleClass="p-sidebar-md"
              [baseZIndex]="10000"
              appendTo="body"
            >
              <app-google-map (onClose)="toggleGoogleMapPopUp()" *ngIf="showGoogleMapSideBar" [addressGroup]="pickUpAddressFormGroup.value" [address]="pickUpFullAddress">
              </app-google-map>
            </p-sidebar>
          </div>
          <div class="col-12">
            <div class="row">
              <div class="col-lg-3 col-md-6 col-12">
                <label>Contact person</label>
                <input class="form-control" type="text" placeholder="Enter contact person" formControlName="contactPersonName" />
                <app-error-messages [control]="pickUpAddressFormGroup.controls.contactPersonName"></app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label>Contact phone</label>
                <input class="form-control" type="text" placeholder="Enter contact phone" formControlName="contactPersonNumber" [mask]="constants.phoneNumberMask" />
                <app-error-messages [control]="pickUpAddressFormGroup.controls.contactPersonNumber"></app-error-messages>
                <span class="text-danger f-s-12" *ngIf="pickUpAddressFormGroup.controls.contactPersonNumber?.errors?.mask">Please enter valid phone number</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section formGroupName="destinationLocation">
      <div class="card p-3 mt-3">
        <div class="title">
          <h4>WHERE IS THE UNIT GOING?</h4>
        </div>
        <div class="row">
          <div class="col-md-6 col-12">
            <label class="required">Address</label>
            <input
              class="form-control"
              type="text"
              ngx-google-places-autocomplete
              ngx-gp-autocomplete
              [options]="options"
              (onAddressChange)="handleDestinationAddressChange($event)"
              placeholder="Enter address"
              formControlName="streetAddress"
            />
            <app-error-messages [control]="destinationAddressFormGroup.controls.streetAddress"></app-error-messages>
          </div>
          <div class="col-lg-2 col-md-6 col-12">
            <label class="required">City</label>
            <input class="form-control" type="text" placeholder="Enter city name" formControlName="city" />
            <app-error-messages [control]="destinationAddressFormGroup.controls.city"></app-error-messages>
          </div>
          <div class="col-lg-2 col-md-6 col-12">
            <label class="required">State</label>
            <input class="form-control" type="text" placeholder="Enter state name" formControlName="state" />
            <app-error-messages [control]="destinationAddressFormGroup.controls.state"></app-error-messages>
          </div>
          <div class="col-lg-2 col-md-5 col-10">
            <label class="required">Zip code</label>
            <input class="form-control" type="text" placeholder="Enter zip code" formControlName="zipcode" />
            <app-error-messages [control]="destinationAddressFormGroup.controls.zipcode"></app-error-messages>
          </div>
          <div class="col-1">
            <div
              class="add-shop map-icon"
              *ngIf="
                destinationAddressFormGroup.get('streetAddress')?.value ||
                destinationAddressFormGroup.get('state')?.value ||
                destinationAddressFormGroup.get('city')?.value ||
                destinationAddressFormGroup.get('zipcode')?.value
              "
            >
              <button class="btn btn-primary" type="button" (click)="toggleDestinationGoogleMapPopUp()">
                <fa-icon [icon]="faIcons.faLocationDot"></fa-icon>
              </button>
            </div>

            <p-sidebar
              [closeOnEscape]="false"
              [dismissible]="false"
              [(visible)]="showDestinationGoogleMapSideBar"
              position="right"
              (onHide)="showDestinationGoogleMapSideBar = false"
              [blockScroll]="true"
              [showCloseIcon]="false"
              styleClass="p-sidebar-md"
              [baseZIndex]="10000"
              appendTo="body"
            >
              <app-google-map
                (onClose)="toggleDestinationGoogleMapPopUp()"
                *ngIf="showDestinationGoogleMapSideBar"
                [addressGroup]="destinationAddressFormGroup.value"
                [address]="destinationFullAddress"
              >
              </app-google-map>
            </p-sidebar>
          </div>
          <div class="col-12">
            <div class="row">
              <div class="col-lg-3 col-md-6 col-12">
                <label>Contact person</label>
                <input class="form-control" type="text" placeholder="Enter contact person" formControlName="contactPersonName" />
                <app-error-messages [control]="destinationAddressFormGroup.controls.contactPersonName"> </app-error-messages>
              </div>
              <div class="col-lg-3 col-md-6 col-12">
                <label>Contact phone</label>
                <input class="form-control" type="text" placeholder="Enter contact phone" formControlName="contactPersonNumber" [mask]="constants.phoneNumberMask" />
                <app-error-messages [control]="destinationAddressFormGroup.controls.contactPersonNumber"> </app-error-messages>
                <span class="text-danger f-s-12" *ngIf="destinationAddressFormGroup.controls.contactPersonNumber?.errors?.mask">Please enter valid phone number</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div class="card p-3 mt-3">
      <div class="title m-t-20">
        <h4>DRIVER INSTRUCTIONS</h4>
      </div>
      <div class="m-t-10">
        <textarea placeholder="Enter driver instructions" rows="5" formControlName="driverInstruction"></textarea>
      </div>
    </div>
    <div class="mt-3">
      <p-tabView>
        <p-tabPanel header="Comments" *ngIf="isViewMode || isEditMode">
          <ng-template pTemplate="content">
            <app-driver-schedule-comment [data]="driverScheduleInfo" [currentUser]="currentUser" [isViewMode]="isViewMode"> </app-driver-schedule-comment>
          </ng-template>
        </p-tabPanel>
        <p-tabPanel header="Attachments">
          <ng-template pTemplate="content">
            <div class="upload-document">
              <div class="d-flex justify-content-end cursor-pointer">
                <button class="btn btn-primary right upload-button" type="button" [disabled]="isViewMode">
                  <input
                  type="file"
                  [disabled]="isViewMode"
                  name="myFile"
                  class="drop-zone__input cursor-pointer"
                  #inputElement
                  (change)="onFileSelect($event)"
                  [accept]="constants.allowedPdfFormats"
                  multiple
                  />
                  <img [src]="constants.staticImages.icons.uploadImage" alt="" />
                  <span>Upload Document </span>
                </button>
              </div>
              <p-table
                class="no-column-selection w-100"
                [value]="fileAttachmentsFormGroup?.value"
                responsiveLayout="scroll"
                sortMode="single"
                [customSort]="true"
                [lazy]="true"
                [reorderableColumns]="true"
                [rowHover]="true"
                [loading]="isLoading"
              >
                <ng-template pTemplate="header" let-columns>
                  <tr>
                    <th scope="col">Name</th>
                    <th scope="col">Actions</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-fileInfo>
                  <tr>
                    <td>
                      {{ fileInfo?.fileName }}
                    </td>
                    <td class="actions">
                      <ng-container *ngIf="fileInfo?.fullPath; else openPdf">
                        <a [href]="fileInfo?.fullPath" [download]="fileInfo?.fullPath" target="_blank">
                          <img [src]="constants.staticImages.icons.viewIcon" alt="" class="view-icon" />
                        </a>
                      </ng-container>
                      <ng-template #openPdf>
                        <a (click)="utils.viewUploadedPdf(fileInfo?.file)">
                          <img [src]="constants.staticImages.icons.viewIcon" alt="" class="view-icon" />
                        </a>
                      </ng-template>
                      <ng-container *ngIf="fileInfo?.fullPath; else downloadFile">
                        <a (click)="downloadServerPDF(fileInfo)" alt="">
                          <img class="view-icon" [src]="constants.staticImages.icons.download" alt="" />
                        </a>
                      </ng-container>
                      <ng-template #downloadFile>
                        <a (click)="utils.downloadUploadedPdf(fileInfo?.file)">
                          <img [src]="constants.staticImages.icons.download" alt="" class="view-icon" />
                        </a>
                      </ng-template>
                      <img [src]="constants.staticImages.icons.deleteIcon" alt="" class="view-icon" *ngIf="!isViewMode" (click)="deleteImageFromCloud(fileInfo?.url)" />
                    </td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                  <td [colSpan]="6" class="no-data">No data to display</td>
                </ng-template>
              </p-table>
            </div>

            <div *ngFor="let fileProgress of fileUploadProgresses; let fileIndex = index">
              <div class="files" *ngIf="!fileProgress.isResolved">
                <div class="file-box-wrapper">
                  <div class="file-box" [ngClass]="{ 'in-progress': !fileProgress?.isResolved }">
                    <button class="btn pdf-icon" id="addModelBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.pdfIcon"></button>
                  </div>
                  <div class="file-progress">
                    {{ fileProgress?.fileProperty?.name }}
                    <p-progressBar [value]="fileProgress?.progress$ | async" [showValue]="true"></p-progressBar>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
    <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="!isViewMode">Save</button>
    <ng-container *appHasPermission="[permissionActions.UPDATE_DRIVER_SCHEDULE]">
      <button class="btn btn-primary" type="submit" appShowLoaderOnApiCall *ngIf="isViewMode">Edit</button>
      <button class="btn btn-primary" type="button" *ngIf="!isEditMode" (click)="onSubmitAndAddNew()" appShowLoaderOnApiCall>Save & Add New</button>
    </ng-container>
  </div>
</form>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateModal"
  position="right"
  (onHide)="showCreateModal = false"
  [fullScreen]="true"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-driver-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal"></app-driver-add>
</p-sidebar>
<p-confirmDialog [transitionOptions]="'0ms'"> </p-confirmDialog>
