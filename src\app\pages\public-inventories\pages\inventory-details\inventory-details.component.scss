@import '/src/assets/scss/theme/mixins';

header {
  display: flex;
  background-color: var(--public-page-nav-color);
  color: var(--card-text-color);
  align-items: center;
  box-shadow: var(--box-shadow);
  padding-top: 10px;
  padding-bottom: 10px;

  .logo {
    display: flex;
    align-items: center;

    img {
      padding: 0px 25px;
      width: 140px;
      height: 30px;
    }

    &:hover {
      cursor: pointer;
    }

    .dealer-name {
      height: 100%;
      font-weight: 500;
      @include flex-center;
      margin-left: 15px;
      padding: 3px;
    }

    .back-button {
      cursor: pointer;
      font-size: 20px;
    }
  }
}

.inventory-detail-wrapper {
  .inventory-wrapper {
    min-height: calc(100vh - 94px);
    width: 100%;
    --bs-gutter-x: 0;
    --bs-gutter-y: 0;
    // TODO Will be remove once complete
    // .img-wrapper {
    //   cursor: pointer;

    //   .image-content {
    //     img {
    //       width: 100%;
    //       height: auto;
    //     }
    //   }

    //   .image-content:first-child {
    //     img {
    //       max-height: 500px;
    //     }
    //   }

    //   .no-image-found {
    //     width: 100%;
    //     height: auto;
    //     max-height: 500px;
    //     margin-top: 2rem;
    //   }
    // }
    .img-wrapper {
      cursor: pointer;

      .image-content {
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 100%;
          height: auto;
          border-radius: 8px;
        }
      }

      .no-image-found {
        width: 100%;
        height: auto;
        max-height: 500px;
        margin-top: 2rem;
        object-fit: contain;
        border-radius: 8px;
        margin-left: 10px;
        object-fit: contain;
        display: block;
      }
    }

    @media (min-width: 1000px) {
      .inventory-images {
        background: var(--public-page-bg-color);
      }

      .inventory-details {
        background: var(--public-page-bg-color);
        padding-left: 20px !important;
      }
    }

    .inventory-details {
      padding: 10px;

      .basic-details {
        .model-name,
        .model-description {
          color: var(--card-text-color);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .model-name {
          font-weight: 600;
          font-size: 22px;
          margin-bottom: 6px;
        }

        .model-description {
          color: #717171;
          font-weight: 400;
          font-size: 18px;
          margin-bottom: 15px;
        }

        .model-conditional-details {
          font-weight: 400;
          font-size: 15px;

          .miles {
            display: flex;
            align-items: center;
            color: var(--card-text-color);

            img {
              width: 25px;
              height: 18px;
              margin-right: 7px;
            }
          }
        }
      }

      .retail-price {
        padding-top: 20px;
      }

      .general-information-wrapper {
        padding-top: 20px;

        .information-title {
          font-size: 17px;
          color: var(--card-text-color);
        }

        .general-information {
          display: flex;
          padding-top: 10px;
          font-size: 14px;

          .info-label {
            margin-right: 80px;
          }
        }
      }

      .specification-information-wrapper {
        padding-top: 20px;

        .specification-information,
        .document-information {
          display: flex;
          padding-top: 10px;
          font-size: 14px;

          .info-label {
            margin-right: 80px;
          }
        }

        .document-information {
          padding-bottom: 10px;

          img {
            height: 20px;
            width: 20px;
            margin-right: 10px;
          }
        }
      }

      .model-detail-label {
        color: #939393;
        margin-right: 2px;
      }
    }
  }

  ::ng-deep p-accordion {
    .p-accordion-header-link {
      background: transparent !important;
      background-color: transparent !important;
      padding: 0 1.25rem 0 0 !important;
      border-color: none !important;
      box-shadow: none !important;

      .accordion-header {
        font-size: 17px;
        color: var(--card-text-color);
        font-weight: 400;
      }
    }

    .p-accordion-content {
      border: none !important;
      padding-left: 0 !important;
      padding-top: 0 !important;
      background-color: var(--public-page-bg-color);
    }

    .p-accordion-tab {
      margin-bottom: 0;
    }

    .specification-information-wrapper .p-accordion-tab {
      border-bottom: 1px solid #e3e3e3;
      margin-bottom: 15px;
    }
  }
}

.close-icon {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
  font-size: 25px;
}

.model-detail-info {
  color: var(--card-text-color);
}

::ng-deep {
  // .p-sidebar.p-sidebar-active {
  //   background-color: var(--public-page-bg-color) !important;
  // }

  .p-accordion .p-accordion-content {
    color: var(--card-text-color);
  }
}

::ng-deep .inventory-detail-wrapper {
  footer {
    .version-info {
      color: var(--card-text-color) !important;
    }
    background-color: var(--public-page-nav-color);
    color: var(--card-text-color);
  }
}

@media only screen and (max-width: 576px) {
  .action-btns {
    margin-top: 15px;

    .action-btn {
      padding: 0 10px !important;
      .text-span {
        display: none;
      }
    }
  }
}

::ng-deep {
  .p-dialog {
    .p-dialog-header {
      background-color: white;
      color: $public-page-active-color;
      font-weight: bold;
      padding: 0.75rem 1.5rem;

      .p-dialog-header-icon {
        color: $public-page-active-color;
      }
    }
    .p-dialog-content {
      background: #f7f4ff;
      padding: 0 1rem;
    }
  }

  .p-card {
    box-shadow: var(--card-box-shadow);
    border-radius: 10px;
    .p-card-body {
      padding: 0 1.25rem;
    }
  }
}

@media print {
  .print-hide {
    display: none;
  }
}

.listing-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: $public-page-active-color;
}
::ng-deep .ql-toolbar.ql-snow {
  padding: 0px;
  border: none;
}