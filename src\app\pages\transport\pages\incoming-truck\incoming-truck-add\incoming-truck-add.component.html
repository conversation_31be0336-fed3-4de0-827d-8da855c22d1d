<div class="incoming-add">
  <form>
    <section [formGroup]="incomingTruckFormGroup">
      <div class="content">
        <div class="add-wraper-tab pb-5 pt-3 px-3">
          <p-tabView [(activeIndex)]="activeIndex" [scrollable]="true">
            <p-tabPanel [header]="ACQUISITION">
              <app-acquisition-info
                [incomingTruckDetails]="incomingTruckDetails"
                [users]="users"
                incomingTruckExpenseForm="incomingTruckExpenseDTO"
                unitDTOForm="unitDTO"
                [categoryTypes]="categoryTypes"
                [isStatusError]="isStatusError"
                [models]="models"
                [makes]="makes"
                [dealerOptions]="dealerOptions"
                [designations]="designations"
                [unitTypes]="unitTypes"
                [isEditMode]="isEditMode"
                [isViewMode]="isViewMode"
                [acquisitionType]="acquisitionType"
                [loaders]="loaders"
                [vendors]="vendors"
                [purchaseByList]="purchaseByList"
                [showAttachmentsTab]="showAttachmentsTab"
                [fileUploadProgresses]="fileUploadProgresses"
                [currentUser]="currentUser"
                (checkedChange)="onAcquisitionCheckedChange($event)"
                (getVendorListEvent)="onGetVendorList()"
                (getUnitTypesEvent)="onGetUnitTypes($event)"
                (getMakeListEvent)="onGetMakeList($event)"
                (getModelListEvent)="onGetModelList($event)"
                (changeUploadFlag)="changeUploadFlag($event)"
                (changeParentCategory)="changeCategory($event)"
              >
              </app-acquisition-info>
            </p-tabPanel>
            <p-tabPanel [header]="PAYMENT" [disabled]="!enablePaymentTab">
              <app-payment-info
                [incomingTruckDetails]="incomingTruckDetails"
                [users]="users"
                [currentUser]="currentUser"
                [isEditMode]="isEditMode"
                [isViewMode]="isViewMode"
                [status]="status"
                [generalTabStatus]="enablePaymentTab"
                paymentForm="paymentProcessingDTO"
                [categoryTypes]="categoryTypes"
                [unitTypes]="unitTypes"
                [makes]="makes"
                [models]="models"
                [isStatusError]="isStatusError"
                [dealerOptions]="dealerOptions"
                [designations]="designations"
                [categoryId]="categoryId"
                [incomingTruckDetails]="incomingTruckDetails"
                (checkedChange)="onGeneralCheckedChange($event)"
                (getUnitTypesEvent)="onGetUnitTypes($event)"
                (getMakeListEvent)="onGetMakeList($event)"
                (getModelListEvent)="onGetModelList($event)"
                (changeParentCategory)="changeCategory($event)"
              >
              </app-payment-info>
            </p-tabPanel>
            <p-tabPanel [header]="PICKUP" [disabled]="!enablePickupTab">
              <app-pickup-info
                driverScheduleAddressForm="driverScheduleAddressDTO"
                [isViewMode]="isViewMode"
                [pickupNotes]="pickupNotes"
                [status]="status"
                [pickupDate]="pickupDate"
                (statusChanged)="statusChanged($event)"
                (pickupNotesChanged)="handelPickupNotesChanged($event)"
                (pickupDateChanged)="handelPickupDateChanged($event)"
              >
              </app-pickup-info>
            </p-tabPanel>
            <p-tabPanel header="Communication" headerStyleClass="communication" [disabled]="!incomingTruckDetails?.id">
              <ng-template pTemplate="content">
                <app-communication [inventoryIncomingInfo]="incomingTruckDetails"></app-communication>
              </ng-template>
            </p-tabPanel>
          </p-tabView>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" type="button" (click)="onCancel()">Cancel</button>
        <button class="btn btn-primary d-flex align-items-center" type="submit" (click)="onSubmit(true, $event)" *ngIf="!isViewMode">
          Save <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="saveLoader"></fa-icon>
        </button>
        <ng-container *appHasPermission="[permissionActions.UPDATE_INCOMING_TRUCK]">
          <button class="btn btn-primary" type="submit" (click)="showAttachmentsTab = true; onSubmit(true, $event)" *ngIf="isViewMode && !incomingTruckHasArrived">Edit</button>
          <button class="btn btn-primary d-flex align-items-center" type="button" *ngIf="!isEditMode && !isViewMode" (click)="onSubmitAndAddNew($event)">
            Save & Add New <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="saveAndAddLoader"></fa-icon>
          </button>
        </ng-container>
      </div>
    </section>
  </form>
</div>
<p-confirmDialog acceptLabel="Proceed" rejectLabel="Cancel" styleClass="confirm-dialog" [breakpoints]="{ '960px': '60vw', '640px': '90vw' }" [style]="{ width: '30vw' }">
</p-confirmDialog>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>
<p-sidebar
  class="dealer"
  [(visible)]="showIncomingCreateModal"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
  (onHide)="showIncomingCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-incoming-truck-add-wrapper
    *ngIf="showIncomingCreateModal"
    [incomingTruckInfo]="null"
    [isViewMode]="false"
    (onClose)="onAddEditIncomingTruckPopupClose($event)"
    [categoriesToDisplay]="categoriesToShow"
  >
  </app-incoming-truck-add-wrapper>
</p-sidebar>
<p-dialog header="Warning" [(visible)]="displayBasic" (onHide)="hideWarning()" contentStyleClass="dialog-width" [style]="{ width: '20vw' }" [baseZIndex]="10000">
  <p>{{ message }}</p>
  <ng-template pTemplate="footer" class="general-warning">
    <p-button icon="pi pi-check" (click)="hideWarning()" label="Okay" styleClass="p-button-text center"> </p-button>
  </ng-template>
</p-dialog>
