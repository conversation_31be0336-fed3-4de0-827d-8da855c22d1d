<div class="inventory-condition">
  <form [formGroup]="conditionFormGroup">
    <p-table [columns]="cols" [value]="inventoryConditionFormArray.controls" responsiveLayout="scroll">
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th scope="col" [ngClass]="col.class">
              {{col.header}}
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex" let-inventoryConditionFormArray>
        <ng-container formArrayName="inventoryCondition">
          <ng-container>
            <tr [formGroupName]="rowIndex">
              <td>
                <input readonly class="form-control pointer-event-none" formControlName="unitComponentName">
              </td>
              <td><input type="text" class="form-control" formControlName="condition">
              </td>
              <td>
                <label>
                <img [src]="constants.staticImages.icons.photoAdd" alt="" class="view-icon">
                <input type="file" name="myFile" #inputElement
                  [accept]="constants.allowedImgFormats" (change)="onFileSelect($event, rowIndex, rowData)" multiple
                  [disabled]="isViewMode" capture hidden>
                  </label>
                <img [src]="constants.staticImages.icons.photoView" alt="" class="view-icon"
                  (click)="openCarousel(rowIndex)"
                  [ngClass]="rowData?.value?.unitImages?.length ? 'img-green':'img-gray'">
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </ng-template>
    </p-table>
  </form>
  <app-image-zoom-overlay
    [images]="zoomImages"
    [selectedIndex]="selectedPhotoIndex"
    [isVisible]="isShowCarousel"
    (close)="closeZoomOverlay()"
    (indexChange)="onImageIndexChange($event)">
  </app-image-zoom-overlay>
</div>
