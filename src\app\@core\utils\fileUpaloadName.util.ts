import { Constants } from "@constants/*";
export function getRefactorFileName(fileName: string): string {
  const modifiedFileName = fileName.replace(Constants.regexForSpecialCharacters, '');
  return modifiedFileName;
}
export function allowExtensions(fileName: string, supportExtension: string): boolean {
  if (!fileName) {
    return false;
  }
  const extensionDot = fileName?.lastIndexOf('.');
  if (extensionDot === -1) {
    return false;
  }
  const ext = fileName?.substring(extensionDot + 1).toLowerCase();
  if (!supportExtension.includes(ext)) {
    return false;
  }
  return true;
}