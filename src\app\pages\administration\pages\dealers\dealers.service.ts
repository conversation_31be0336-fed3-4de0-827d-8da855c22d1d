import { Injectable } from "@angular/core";
import { BaseCrudService } from "@core/services";
import { API_URL_UTIL } from "@core/utils";
import { Observable } from 'rxjs';
import { Utils } from "src/app/@shared/services";
import { DealerListItem } from '../../models';

@Injectable({ providedIn: 'root' })
export class DealerService extends BaseCrudService {

  getBaseAPIPath(): string {
    return API_URL_UTIL.admin.dealers.root;
  }

  getDealerList<IdNameModel>(): Observable<IdNameModel[]> {
    return this.httpClient.get<IdNameModel[]>(`${API_URL_UTIL.subDomain.root}/${API_URL_UTIL.admin.dealers.root}/${API_URL_UTIL.admin.dealers.basicInfo}`);
  }

  fromServerModel(json: DealerListItem): DealerListItem {
    if (!json) {
      return new DealerListItem();
    }
    return {
      ...json,
      addressState: json.address?.state,
      contactPersonName: Utils.getFullName(json.contactPerson?.firstName, json.contactPerson?.lastName),
    };
  }
}
