import { GenericFilterParams } from "src/app/@shared/models";
import { Role } from "./role.model";
import { Shop } from "./shop.model";
import { Address, ContactPerson } from "./vendor.model";

export interface DealerGroup {
  id: number;
  dealerId: number;
  dealerName: string;
  skeyeUserId: number;
  roleId: number;
  roleName: string;
  archivedDealer?: boolean;
}

export class DealerListFilter extends GenericFilterParams {
  archived = false;
}

export class DealerListItem {
  id!: number;
  address!: Address;
  contactPerson!: ContactPerson;
  fundingEmails!: FundingEmail[];
  name!: string;
  email!: string;
  organizationId!: string;
  phoneNumber!: string;
  contactPersonName?: string;
  addressState?: string;
  isActive?: boolean;
  archived!: boolean;
  shopNames?: string;
  abbreviation?: string;
  createdDate?: string;
  createdBy?: string;
  logoUrl?: string;
  fullLogoUrl?: string;
}

export class DealerCreateParam {
  additionalDetail!: string;
  address!: Address;
  contactPerson!: ContactPerson;
  fundingEmails!: FundingEmail[];
  email!: string;
  name!: string;
  organizationId!: number;
  phoneNumber!: number;
  logoUrl?: string;
  abbreviation?: string;
  id?: number;
}

export class FundingEmail {
  id!: number;
  email!: string;
  dealerId!: number;
}

export class DealerUserGroupItem {
  id!: number;
  name!: string;
  shops: Shop[] = [];
  selectedShops?: Shop[] = [];
  selectedRoles?: Role[] = [];
  isSelected = false;
  showErrorForRole = false;
  isDefultAccess = false;
  currentlySelectedDealerId!: number;
}

export interface DealerBasicInfoListItem {
  id: number;
  name: string;
}

export interface Shops {
  id: number;
  name: string;
  taskCount: number;
  pipelineCount: number;
  defaultUserId: number;
  defaultUserName: string;
}

export interface FundingEmailConfig {
  id: number;
  label: string;
  isIncluded: boolean;
  isDefault: boolean;
}
