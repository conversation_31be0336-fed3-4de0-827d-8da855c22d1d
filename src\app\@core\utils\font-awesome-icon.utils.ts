import { faFloppyDisk } from '@fortawesome/free-regular-svg-icons';
import { faAdd, faArrowDown, faArrowLeft, faArrowRotateRight, faBars, faCaretDown, faCaretRight, faCheckCircle, faChevronCircleDown, faChevronCircleUp, faCircle, faCircleXmark, faCity, faClockRotateLeft, faColumns, faCopy, faDownload, faEllipsisH, faEllipsisV, faEnvelope, faEnvelopeSquare, faExpandAlt, faFileAlt, faFileDownload, faFilePdf, faFilterCircleXmark, faInfo, faInfoCircle, faLinkSlash, faLocationDot, faMagnifyingGlass, faMinus, faMoon, faPhoneAlt, faRefresh, faShare, faSignOutAlt, faSignal, faSort, faSortDown, faSortUp, faSpinner, faSun, faTimes, faTrashCan, faUser, faXmark } from "@fortawesome/free-solid-svg-icons";


export const faIcons = {
  faSpinner,
  faXmark,
  faFilterCircleXmark,
  faCity,
  faColumns,
  faChevronCircleDown,
  faChevronCircleUp,
  faSignal,
  faFileAlt,
  faEnvelope,
  faPhoneAlt,
  faUser,
  faArrowLeft,
  faCheckCircle,
  faSignOutAlt,
  faEnvelopeSquare,
  faSort,
  faSortDown,
  faSortUp,
  faTimes,
  faBars,
  faInfoCircle,
  faInfo,
  faAdd,
  faDownload,
  faArrowDown,
  faFileDownload,
  faTrashCan,
  faFilePdf,
  faShare,
  faExpandAlt,
  faLocationDot,
  faCaretDown,
  faCaretRight,
  faCircleXmark,
  faEllipsisV,
  faEllipsisH,
  faCircle,
  faLinkSlash,
  faClockRotateLeft,
  faMoon,
  faSun,
  faArrowRotateRight,
  faCopy,
  faFloppyDisk,
  faMinus,
  faRefresh,
  faMagnifyingGlass
}

