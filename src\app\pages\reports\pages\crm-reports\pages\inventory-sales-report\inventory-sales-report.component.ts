import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { dateFormat } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { UnitTypeService } from '@pages/administration/pages/specification-configration/pages/unit-type/unit-type.service';
import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { SoldTruckService } from '@pages/pipeline/pages/sold-truck-board/sold-truck.service';
import { FilterUnitTypes } from '@pages/public-inventories/models';
import { ReportModuleConst } from '@pages/reports/models/reports-const';
import { ReportingService } from '@pages/reports/services/reporting-service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
import { DailySalesListItems } from '../../models/daily-sales';

@Component({
  selector: 'app-inventory-sales-report',
  templateUrl: './inventory-sales-report.component.html',
  styleUrls: ['./inventory-sales-report.component.scss']
})
export class InventorySalesReportComponent extends BaseComponent implements OnInit {

  inventorySalesList: DailySalesListItems[] = [];
  _selectedColumns: ColumnItem[] = [];
  dropDownColumnList: ColumnItem[] = [];
  selectedCategoryId!: number | null;
  accountRepIds!: Array<number>;
  unitTypes !: IdNameModel[];
  unitTypeIds!: Array<number>;
  filterParams: GenericFilterParams = new GenericFilterParams();
  globalSearch = new Subject<FilterValue[]>();
  salesRepList: IdNameModel[] = [];
  categoryTypes!: Array<IdNameModel>;
  invoiceDate!: Date | null;
  dropdownLoaders = {
    salesRep: false,
    unitType: false,
    category: false,
  };
  constructor(
    private readonly commonService: CommonService,
    private readonly reportingService: ReportingService,
    private readonly inventoryService: InventoryService,
    private readonly unitTypeService: UnitTypeService,
    private readonly cdf: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly soldTruckService: SoldTruckService,
  ) {
    super();
    this.pageTitle = 'Inventory Sales Report';
    this.paginationConfig.itemsPerPage = 25;
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }
  set selectedColumns(val: any[]) {
    this._selectedColumns = this.dropDownColumnList.filter(col => val.includes(col));
  }

  ngOnInit(): void {
    this.getDropDownColumnList();
    this.displaySearchResult();
    this.getAccountReportsList();
  }

  getCategoryTypes() {
    if (!this.categoryTypes?.length) {
      this.dropdownLoaders.category = true
      this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
        next: (categoryTypes: Array<IdNameModel>) => {
          this.categoryTypes = categoryTypes;
          this.dropdownLoaders.category = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  onCategoryClear() {
    this.unitTypes = this.unitTypeIds = [];
  }

  getUnitType(): void {
    if (!this.unitTypes?.length) {
      this.dropdownLoaders.unitType = true
      this.unitTypeService.get<Array<FilterUnitTypes>>(`types/${API_URL_UTIL.inventory.list}?categoryId=${this.selectedCategoryId ?? ''}`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: Array<FilterUnitTypes>) => {
            this.unitTypes = res;
            this.dropdownLoaders.unitType = false;
            this.cdf.detectChanges();
          }
        });
    }
  }

  clearUnitTypes(col: ColumnItem): void {
    this.unitTypeIds = [];
    this.tableSearchByColumn([], col);
  }

  private getAccountReportsList(): void {
    this.dropdownLoaders.salesRep = true;
    const endpoint = API_URL_UTIL.reports.salesPersonListOfSalesReport;
    this.reportingService.getSalesPersonList(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (salesRepList) => {
        this.salesRepList = salesRepList;
        this.dropdownLoaders.salesRep = false;
      },
      error: () => {
        this.dropdownLoaders.salesRep = false;
      }
    });
  }

  tableSearchByColumn(event: any, col: any): void {
    this.isLoading = true;
    this.inventorySalesList = [];
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(values => {
        if (Array.isArray(values.value) && !values.value.length) {
          return false;
        }
        return true;
      });
    }
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  private setValueForReset(input: string, col: any): void {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else {
      if (temp) {
        temp.value = input;
      }
    }
    if (temp1) {
      temp1.value = temp?.value;
    }
  }

  private setSearchEndDate(event: any, col: any): void {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.key,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  private setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  private assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN;
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER;
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE;
        break;
      case 'DATE':
        stringDataType = DataType.DATE;
        break;
      case 'DROP_DOWN':
        stringDataType = col?.searchKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG;
        break;
      default:
        stringDataType = DataType.STRING;
        break;
    }
    return stringDataType
  }

  private getFilterInfo(inputValue: any, col: any): void {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.searchKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.searchKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'ALL' || inputValue === '')) {
      const rm = this.filterParams.values.find((d: any) => d.key === 'status')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }
    }
  }

  private assignOperator(type: string): string {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
      case 'DOUBLE':
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }

  private setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  private getEventValue(event: any, col: any): string {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.searchKey === 'status' ? event?.value?.split()?.join('_')?.toUpperCase() : event?.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  private displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
      this.getAll();
    });
  }

  private getDropDownColumnList(): void {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${ReportModuleConst.INVENTORY_SALES_REPORT}`);
    this.commonService.getListFromObject<ColumnItem[]>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        this._selectedColumns = response;
        this.cdf.detectChanges();
      });
  }

  getAll() {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    const endpoint = `${API_URL_UTIL.reports.dailySales}/${API_URL_UTIL.reports.filter}`
    this.reportingService.getListWithFiltersWithPagination<GenericFilterParams, DailySalesListItems>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.inventorySalesList = res.content;
          this.setPaginationParamsFromPageResponse<DailySalesListItems>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    const endpoint = `${API_URL_UTIL.reports.dailySales}/${API_URL_UTIL.reports.filter}`
    this.reportingService.getListWithFiltersWithPagination<GenericFilterParams, DailySalesListItems>
      (this.filterParams, 1, this.paginationConfig.totalElements, endpoint)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Inventory_Sales_Report");
          this.isExporting = false;
        });
      });
  }

  getExcelData(data: Array<DailySalesListItems>) {
    return data.map(res => ({
      'Stock#': res?.stockNumber,
      'Category': res?.category?.name,
      'Unit Type': res?.unitType?.name,
      'Salesperson': res?.salesPerson?.name,
      'Total Projected Investment': Utils.formatCurrency(res?.totalProjectedInvestment),
      'Sale Price': Utils.formatCurrency(res?.sellPrice),
      'Invoice Date': Utils.dateIntoUserReadableFormat(res?.invoiceDate ?? '')
    }));
  }

  clearSalesRep(col: ColumnItem): void {
    this.accountRepIds = [];
    this.tableSearchByColumn([], col);
  }

  clearSearchInput(): void {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.invoiceDate = null;
    this.selectedCategoryId = null;
    this.accountRepIds = this.unitTypeIds = this.filterParams.values = [];
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  clearDate() {
    this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "invoiceDate");
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }
}
