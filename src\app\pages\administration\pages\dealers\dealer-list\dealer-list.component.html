<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.UPDATE_DEALERS]" (click)="toggleEmailFieldSidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Funding Email Config</span>
    </button>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button class="btn btn-primary left" (click)="onAdd()" *appHasPermission="[permissionActions.CREATE_DEALERS]" [appImageIconSrc]="constants.staticImages.icons.addNew">
      <span class="show-label">Add New Dealer</span>
    </button>
  </div>
</app-page-header>

<div class="card">
  <div class="tabs">
    <tabset #profileTabs>
      <tab heading="Active" #activeTab="tab" (selectTab)="onTabChanged($event)">
        <ng-container [ngTemplateOutlet]="activeTabTemplate" *ngIf="activeTab.active"></ng-container>
      </tab>

      <tab heading="Archived" #archivedTab="tab" (selectTab)="onTabChanged($event)">
        <ng-container [ngTemplateOutlet]="activeTabTemplate" *ngIf="archivedTab.active"></ng-container>
      </tab>
    </tabset>
  </div>
</div>

<div class="card">
  <ng-template #activeTabTemplate>
    <p-table
      [resizableColumns]="true"
      styleClass="p-datatable-gridlines"
      class="no-column-selection"
      [columns]="selectedColumns"
      [value]="dealers"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'name'"
      [rowHover]="true"
      [loading]="isLoading"
      [scrollable]="true"
      scrollDirection="horizontal"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th pResizableColumn pFrozenColumn scope="col" class="small-col">Logo</th>
          <th pResizableColumn scope="col" pSortableColumn="name" id="name" pFrozenColumn>Name<p-sortIcon field="name"></p-sortIcon></th>
          <th pResizableColumn scope="col" pSortableColumn="email" id="email">Email<p-sortIcon field="email"></p-sortIcon></th>
          <th pResizableColumn scope="col" pSortableColumn="contactPerson.firstName" id="contactPersonName">
            Contact Person <p-sortIcon field="contactPerson.firstName"></p-sortIcon>
          </th>
          <th pResizableColumn scope="col">Phone Number</th>
          <th pResizableColumn scope="col">Address</th>
          <th pResizableColumn scope="col">Public Url</th>
          <th pResizableColumn scope="col" id="fundingEmail">Funding Email</th>
          <th pResizableColumn scope="col" class="small-col" *appHasPermission="[permissionActions.UPDATE_DEALERS]">Active</th>
          <th pResizableColumn scope="col" class="small-col" *appHasPermission="[permissionActions.UPDATE_DEALERS]">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-dealer let-columns="columns">
        <tr>
          <td class="logo-cell" pFrozenColumn>
            <div class="dealer-logo-thumbnail" *ngIf="dealer?.fullLogoUrl; else noLogo">
              <img [src]="dealer?.fullLogoUrl" [alt]="dealer?.name + ' logo'" (click)="onViewLogo(dealer?.fullLogoUrl)" />
            </div>
            <ng-template #noLogo>
              <div class="dealer-logo-thumbnail">
                <img [src]="constants.staticImages.noImages" class="inventory-list-image" alt="Dealer logo" />
              </div>
            </ng-template>
          </td>
          <td pFrozenColumn>{{ dealer?.name }}</td>
          <td>{{ dealer?.email }}</td>
          <td>{{ dealer?.contactPersonName }}</td>
          <td>{{ dealer?.phoneNumber | phone }}</td>
          <td>
            {{
              (dealer?.address?.streetAddress ? dealer?.address?.streetAddress + ', ' : '') + (dealer?.address?.city ? dealer?.address?.city + ', ' : '') + dealer?.address?.state
            }}
          </td>
          <td>
            <a target="_blank" [href]="environment.forntendUrl + 'public-inventory/' + dealer?.abbreviation">
              {{ environment.forntendUrl + 'public-inventory/' + dealer?.abbreviation }}
            </a>
          </td>
          <td>
            <ul class="fundingEmail">
              <ng-container *ngFor="let fundingEmail of dealer.fundingEmails">
                <li>{{ fundingEmail.email }}</li>
              </ng-container>
            </ul>
          </td>
          <td *appHasPermission="[permissionActions.UPDATE_DEALERS]" class="actions">
            <div class="actions-content">
              <ui-switch [(ngModel)]="dealer.isActive" [loading]="selectedDealer?.id === dealer.id && isArchiveInProgress" (change)="onArchive(dealer, $event)">
                <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedDealer?.id === dealer.id"></fa-icon>
              </ui-switch>
            </div>
          </td>
          <td *appHasPermission="[permissionActions.UPDATE_DEALERS]" class="actions">
            <div class="actions-content">
              <img [src]="isActiveTab ? constants.staticImages.icons.edit : constants.staticImages.icons.viewIcon" (click)="onEdit(dealer)" alt="" />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </ng-template>
</div>

<p-sidebar
  class="dealer"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-dealer-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateModal" [dealerInfo]="selectedDealer"></app-dealer-add>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showEmailFieldModal"
  position="right"
  (onHide)="showEmailFieldModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-funding-email-config *ngIf="showEmailFieldModal" (onClose)="toggleEmailFieldSidebar()"></app-funding-email-config>
</p-sidebar>

<p-confirmPopup appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmPopup>

<app-image-zoom-overlay
  [images]="zoomImages"
  [selectedIndex]="selectedPhotoIndex"
  [isVisible]="isShowCarousel"
  (close)="closeZoomOverlay()"
  (indexChange)="onImageIndexChange($event)">
</app-image-zoom-overlay>
