<app-page-header [pageTitle]="pageTitle">
  <div class="top-header" headerActionBtn>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="exportUsersToExcel()"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
  </div>
</app-page-header>
<div class="card tabs stock-truck-list">
  <form [formGroup]="dateFilterFormGroup" class="date-range-form pt-3 px-4" (ngSubmit)="onSubmit()">
    <div class="content d-flex flex-wrap justify-content-between">
      <div class="d-flex flex-wrap align-items-center gap-2">
        <div formGroupName="startDateGroup">
          <label>From</label>
          <p-calendar
            appendTo="body"
            formControlName="value"
            placeholder="mm/dd/yyyy"
            [showIcon]="true"
            [showButtonBar]="true"
            [readonlyInput]="true"
            inputId="startDateIcon"
            [maxDate]="dailySalesEndDateFormGroup.controls?.value?.value"
          ></p-calendar>
        </div>
        <div formGroupName="endDateGroup">
          <label>To</label>
          <p-calendar
            appendTo="body"
            formControlName="value"
            placeholder="mm/dd/yyyy"
            [showIcon]="true"
            [showButtonBar]="true"
            [readonlyInput]="true"
            inputId="endDateIcon"
            [minDate]="dailySalesStartDateFormGroup.controls?.value?.value"
          ></p-calendar>
        </div>
        <div class="mt-4 d-flex align-items-center flex-wrap gap-2">
          <button class="btn btn-primary">Go</button>
          <button type="button" class="btn btn-primary btn-sm" (click)="clearSearchInput()">Reset filters</button>
        </div>
      </div>
      <div class="mt-4 d-flex align-items-center flex-wrap gap-2">
        <button type="button" class="btn btn-primary btn-sm d-flex align-items-center" icon="pi pi-plus"
          (click)="expandAll()">
          <em class="pi pi-plus me-2"></em>
          Expand All
        </button>
        <button type="button" class="btn btn-primary btn-sm d-flex align-items-center" icon="pi pi-minus"
          (click)="collapseAll()">
          <em class="pi pi-minus me-2"></em>
          Collapse All
        </button>
      </div>
    </div>
  </form>
  <div class="tab-content">
    <p-table
      [columns]="selectedColumns"
      styleClass="p-datatable-gridlines"
      [value]="activityList"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      dataKey="id"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [resizableColumns]="true"
      columnResizeMode="expand"
      class="has-date-range-filter"
      [expandedRowKeys]="expandedRows"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn pReorderableColumn [pSortableColumn]="col?.shortingKey" scope="col">{{ col.name }}<p-sortIcon [field]="col?.shortingKey"></p-sortIcon></th>
          </ng-container>
        </tr>
        <tr class="report-search-tr">
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn scope="col">
              <span class="search-input" *ngIf="col.type === 'STRING'">
                <input pInputText placeholder="Search {{ col.name }}" type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DOUBLE'">
                <input
                  pInputText
                  placeholder="Search {{ col.name }}"
                  type="number"
                  class="form-control"
                  (input)="tableSearchByColumn($event.target, col)"
                  [(ngModel)]="col.value"
                />
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'"> </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN'">
                <p-multiSelect
                  [options]="salesRepList"
                  defaultLabel="Select a {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="accountRepIds"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.salesRep, data: salesRepList }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="col.value?.length" (click)="clearSalesRep(col)"></fa-icon>
              </span>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-saleData let-columns="columns" let-rowData let-rowIndex="rowIndex" let-expanded="expanded">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td>
              <span *ngIf="col.type !== 'DATE' && col.key !== 'firstName'">
                {{ getEvaluatedExpression(col.key, saleData) }}
              </span>
              <span *ngIf="col.type === 'DATE'">
                {{ getEvaluatedExpression(col.key, saleData) | date : constants.dateFormat }}
              </span>
              <span *ngIf="col.key === 'firstName'" class="d-flex align-items-center">
                <p-button type="button" pRipple [pRowToggler]="saleData" [text]="true" [rounded]="true" [plain]="true"
                  [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'" />
                {{ getEvaluatedExpression(col.key, saleData) }}
              </span>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-saleData>
        <tr>
          <td colspan="7">
            <div *ngIf="saleData.historyMessage.length; else historyFound">
              <ul class="px-5 py-1 d-flex" *ngFor="let history of saleData.historyMessage">
                <li>
                  {{ history.message }}
                  ~
                  <span class="text-muted mx-1 fs-12">
                    {{ history.date | dateAgo }}
                  </span>
                  |
                  <span class="text-muted mx-1 fs-12">
                    {{ history.module | titlecase }}
                  </span></li>
              </ul>
            </div>
            <ng-template #historyFound>
              <div class="px-5 py-1 d-flex">
                No activity found
              </div>
            </ng-template>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="dropDownColumnList.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>
