import { ChangeDetectorRef } from '@angular/core';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { ParsedData } from '@pages/shops/models';
import * as FileSaver from 'file-saver';
export class Utils {

  static getFullName(firstName: string | undefined, lastName: string | undefined): string {
    return `${firstName || ''} ${lastName || ''}`;
  }

  static getInitials(firstName: string | undefined, lastName: string | undefined): string {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`;
  }

  static deepFlatten(arr: unknown[], depth = 1): unknown {
    return depth > 0 ? arr.reduce((acc: string | any[], val: any) => acc.concat(Array.isArray(val) ? this.deepFlatten(val, depth - 1) : val), [])
      : arr.slice();
  }

  static getPropertyByString(object: any, propString: string) {
    let value = object;
    if (propString !== null) {
      const props = propString.split('.');
      for (const prop of props) {
        if (value?.[prop] === undefined) {
          value = '';
          break;
        }
        value = value?.[prop] ?? '';
      }
    }
    return value;
  }

  static downloadFromUrl(url: string, fileName: string): void {
    FileSaver.saveAs(url, fileName, { autoBom: false });
  }

  static saveAsExcelFile(buffer: any, fileName: string): void {
    const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    const EXCEL_EXTENSION = '.xlsx';
    const data: Blob = new Blob([buffer], {
      type: EXCEL_TYPE
    });
    FileSaver.saveAs(data, `${fileName}_${new Date().toLocaleString()}${EXCEL_EXTENSION}`, { autoBom: false });
  }

  static dateIntoUserReadableFormat(date: string | Date): string | Date {
    if (typeof date === 'string' && date.includes('T')) {
      const [year, month, day] = date.split('T')[0].split('-');
      return `${month}-${day}-${year}`;
    }
    return date;
  }

  static formatCurrency(value: number) {
    if (value === null || value === undefined) {
      return null;
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }

  static convertIntoLocalDate(date: string | Date): string | Date {
    return new Date(date).toLocaleDateString().replace(/\//g, '-')
  }

  static compareTwoObjects(oldValue: Object, newValue: Object): ParsedData[] {
    const comparedData: ParsedData[] = [];
    for (const oKey in oldValue) {
      const objKey = oKey as keyof typeof oldValue;
      if (oldValue[objKey] !== newValue[objKey]) {
        comparedData.push({ fieldLabel: objKey, oldValue: String(oldValue[objKey]) === 'null' ? '-' : String(oldValue[objKey]), newValue: String(newValue[objKey]) === 'null' ? '-' : String(newValue[objKey]) });
      }
    }
    return comparedData;
  }

  static getRole(roleKey: string): string {
    if (roleKey.includes("_")) {
      return roleKey.split("_")[1];
    }
    return roleKey;
  }

  static handlePopoverBackdropClick(element: HTMLElement, reference: any, referenceVariableKey: string, valueToBeSet: boolean, cdf?: ChangeDetectorRef): void {
    if (!element.classList.contains('p-confirm-popup-content') && reference) {
      reference[referenceVariableKey] = valueToBeSet;
      if (cdf) {
        cdf.detectChanges();
      }
    }
  }


  static viewUploadedPdf(fileData: File | undefined): void {
    if (fileData) {
      const url = window.URL.createObjectURL(fileData);
      window.open(url, "_blank")
    }
  }

  static downloadUploadedPdf(fileData: File | undefined): void {
    if (fileData) {
      const url = window.URL.createObjectURL(fileData);
      FileSaver(url, fileData.name);
    }
  }

  static hasSubModulePermission(allPermission: PrivilegeActionResponseDTOs[], permission: string[]): boolean {
    if (allPermission?.some((permissionItem) => permissionItem.module.name === 'Skeye')) {
      return true;
    }
    return allPermission?.some((permissionItem: PrivilegeActionResponseDTOs) => {
      return permission.includes(permissionItem.actionDto.name);
    });
  }

  static hasModulePermission(allPermission: PrivilegeActionResponseDTOs[], moduleName: string): boolean {
    if (allPermission?.some((permissionItem) => permissionItem.module.name === 'Skeye')) {
      return true;
    }
    return allPermission?.some((permissionItem: PrivilegeActionResponseDTOs) => {
      return permissionItem.module.name === moduleName;
    });
  }
}

export function getter() {
  let current = arguments[0];
  for (let i = 1; i < arguments.length; i++) {
    if (current[arguments[i]]) {
      current = current[arguments[i]];
    } else {
      return null;
    }
  }
  return current;
}
