import { DatePipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, dateFormat, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { MakeModel, Model, UnitType } from '@pages/administration/pages/specification-configration/models/specification.model';
import { MakeModelService } from '@pages/administration/pages/specification-configration/pages/make-model/make-model.service';
import { UnitTypeService } from '@pages/administration/pages/specification-configration/pages/unit-type/unit-type.service';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { CustomerCopyStatusList, CustomerLeadListFilter, CustomerLeadListItem, CustomerStatusList } from '@pages/crm/models/customer-lead.model';
import { CrmService } from '@pages/crm/services/crm.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
@Component({
  selector: 'app-crm-customer-list',
  templateUrl: './crm-customer-list.component.html',
  styleUrls: ['./crm-customer-list.component.scss'],
  providers: [TitleCasePipe]
})
export class CrmCustomerListComponent extends BaseComponent implements OnInit {
  crmCustomerLeadList: CustomerLeadListItem[] = [];
  filterParams: CustomerLeadListFilter = new CustomerLeadListFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModulesName = [HistoryModuleName.CUSTOMER_LEAD, HistoryModuleName.QUOTE];
  showConfirmPopup = true;
  customerStatusList = CustomerStatusList;
  statusCopyList = CustomerCopyStatusList;
  isStatusList!: number | null;
  status!: string;
  isMouseHover!: any;
  crmId!: string;
  leadId!: string;
  creationDate!: Date | null;
  selectedCrmCustomerList!: CustomerLeadListItem | null;
  isCrmCustomerViewMode = false;
  showColumnModal = false;
  dropDownColumnList: ColumnItem[] = [];
  tableColumn!: FilterList | undefined;
  defaultColumnsArray: ColumnItem[] = [];
  _selectedColumns: any[] = [];
  cols: any[] = [];
  defaultTabs!: FilterList[];
  hideFieldForSearch: string | null = '';
  globalSearch = new Subject<FilterValue[]>();
  initialFilterParams: any;
  makesMasterData: Array<MakeModel> = [];
  modelsMasterData: Array<Model> = [];
  unitTypesMasterData: Array<UnitType> = [];
  makes: Array<MakeModel> = [];
  models: Array<Model> = [];
  unitTypes: Array<UnitType> = [];
  categoryTypes!: Array<IdNameModel>;
  selectedCategoryId!: number | undefined;
  makeIds: Array<number> = [];
  modelIds: Array<number> = [];
  unitTypeIds: Array<number> = [];
  userPermissions!: Array<PrivilegeActionResponseDTOs>;
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columns",
      command: () => this.exportUsersToExcel(true)
    }
  ];

  dropdownLoaders = {
    make: false,
    model: false,
    unitType: false,
    category: false
  }

  constructor(
    private readonly crmService: CrmService,
    private readonly authService: AuthService,
    private readonly commonService: CommonService,
    private readonly columnDropDownService: ColumnDropdownService,
    private readonly datePipe: DatePipe,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly titleCasePipe: TitleCasePipe,
    private readonly activeRoute: ActivatedRoute,
    private readonly makeModelService: MakeModelService,
    private readonly unitTypeService: UnitTypeService,
    private readonly inventoryService: InventoryService
  ) {
    super();
    this.pageTitle = 'Customer Lead';
  }

  async ngOnInit() {
    await this.getCurrentUser();
    this.getFilterDetail();
    this.getFilterSaveParams();
    this.displaySearchResult();
    this.getAllFilterData();
    this.initialFilterParams = Object.assign([], this.filterParams.values)
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id && params.returnUrl) {
          this.getCrmDetails(params.id);
        }
      });
    this.userPermissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
  }

  private getAllFilterData(): void {
    this.getAllMake();
    this.getAllUnitType();
    this.getAllModels();
  }

  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }
  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }
  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.crmService.getListWithFiltersWithPagination<CustomerLeadListFilter, CustomerLeadListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.admin.crm.customerFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.crmCustomerLeadList = res.content;
          this.setPaginationParamsFromPageResponse<CustomerLeadListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  onAdd(): void {
    this.showCreateModal = true;
    this.showConfirmPopup = false;
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.showConfirmPopup = true;
    this.selectedCrmCustomerList = null;
    if (refreshList) {
      this.getAll();
      this.getAllFilterData();
    }
  }

  onDelete(customerData: CustomerLeadListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'crm customer data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(customerData);
      }
    });
  }

  onDeleteConfirmation(customerData: CustomerLeadListItem): void {
    const endpoint = API_URL_UTIL.admin.crm.customer;
    this.crmService.delete(customerData.id, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.customerLeadDataDeleted);
          this.getAll();
        }
      });
  }

  getCrmDetails(id: string): void {
    this.crmService.get<CustomerLeadListItem>(id, API_URL_UTIL.admin.crm.customerLead)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CustomerLeadListItem) => {
          this.onViewEdit(res, false);
        }
      });
  }

  onViewEdit(crm: CustomerLeadListItem, isEdit: boolean): void {
    this.showConfirmPopup = false;
    this.isCrmCustomerViewMode = !isEdit;
    this.selectedCrmCustomerList = crm;
    this.crmId = crm.id;
    this.showCreateModal = true;
  }

  getStatusName(status: string) {
    return this.crmService.getStatusName(status);
  }

  findStatusIndex(index: number): void {
    this.isStatusList = index;
  }

  changeStatus(status: any, id: number): void {
    const endpoint = API_URL_UTIL.admin.crm.customerStatus.replace(':customerId', id.toString());
    const fullEndpoint = `${API_URL_UTIL.admin.crm.customer}${endpoint}status=${status}`;
    this.crmService.patch(Number(null), fullEndpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.statusChangeSuccess);
      this.isStatusList = null;
      this.getAll();
    })
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  clearDate() {
    this.filterParams.values = this.filterParams.values.filter(param => param.key !== "createdDate");
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  createDefaultColumns(): void {
    const defaultFields = this.dropDownColumnList.filter(a => a.default ? a.name : null)
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.defaultColumnsArray = JSON.parse(res.data);
            this.sortSelectedColumns()
            this.cdf.detectChanges();
          }
        }
      });
    }
  }
  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: FilterModuleName.CUSTOMER_LEAD.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }
  getDropDownColumnList(shouldCreateDefaultColumn = false) {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${'CUSTOMER_LEAD'}`);
    this.commonService.getListFromObject<ColumnItem>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.sortSelectedColumns()
        }
        this.cdf.detectChanges();
      });
  }

  getFilterDetail(): void {
    this.defaultColumnsArray = [];
    const userId = this.currentUser?.id ?? null
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${FilterModuleName.CUSTOMER_LEAD}`)
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (filters) => {
        this.defaultTabs = filters.filter((item: any) => item.filterType === "TAB");
        this.tableColumn = filters.find((item: any) => item.filterType === "COLUMN")
        if (this.tableColumn && this.tableColumn?.data) {
          this.defaultColumnsArray = JSON.parse(this.tableColumn?.data)
          this.getDropDownColumnList();
          this.cdf.detectChanges();
        } else {
          this.getDropDownColumnList(true);
        }
      }
    });
  }

  sortColumnList(columns: ColumnItem[]) {
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'Lead Id');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'Lead Id' && x.name !== 'Action');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return tempData.concat(tempData1, tempData2);
  }

  sortSelectedColumns() {
    let temp: any[] = [];
    const ids = this.defaultColumnsArray.map(a => a.id);
    this.defaultColumnsArray = this.sortColumnList(this.defaultColumnsArray)
    this._selectedColumns = this.cols = this.defaultColumnsArray;
    if (this.tableColumn && this.tableColumn?.data) {
      temp = JSON.parse(this.tableColumn?.data);
    }
    this.dropDownColumnList.forEach((column: any) => {
      if (!ids.includes(column.id)) {
        temp.push(column);
      }
    });
    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
  }

  getMake(): void {
    this.makes = this.selectedCategoryId ? this.makesMasterData.filter(make => make.categoryId === this.selectedCategoryId) : this.makesMasterData;
  }

  getModels(): void {
    this.models = this.makeIds.length ? this.modelsMasterData.filter(model => this.makeIds.includes(model.makeId)) : this.modelsMasterData;
  }

  getUnitType(): void {
    this.unitTypes = this.selectedCategoryId ? this.unitTypesMasterData.filter(unitType => unitType.unitTypeCategoryId === this.selectedCategoryId) : this.unitTypesMasterData;
  }

  tableSearchByColumn(event: any, col: any) {
    this.isLoading = true;
    this.crmCustomerLeadList = [];
    if (col.shortingKey === 'unitTypeCategory.id') {
      this.onCategoryChange();
      this.filterParams.values = this.filterParams?.values?.filter((value: FilterValue) => {
        return value.key !== "unitType.id" && value.key !== "makes.make.id" && value.key !== "unitModels.unitModel.id"
      })
    }
    if (col.shortingKey === 'makes.make.id') {
      this.modelIds = [];
      this.filterParams.values = this.filterParams?.values?.filter((value: FilterValue) => value.key !== "unitModels.unitModel.id")
    }
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  setValueForReset(input: string, col: any) {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else if (col.key === 'status') {
      temp.value = this.titleCasePipe.transform(input?.split('_')?.join(' '));
    } else if (col.shortingKey === 'unitTypeCategory.id') {
      temp.value = this.selectedCategoryId;
    } else if (col.shortingKey === 'make.id') {
      temp.value = this.makeIds;
    } else if (col.shortingKey === 'unitModel.id') {
      temp.value = this.modelIds;
    } else if (col.shortingKey === 'unitType.id') {
      temp.value = this.unitTypeIds;
    } else {
      if (temp) {
        temp.value = input;
      }
    }
    if (temp1) {
      temp1.value = temp?.value;
    }
  }

  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.shortingKey === 'status' ? event?.value?.split()?.join('_')?.toUpperCase() : event?.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values;
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.shortingKey);
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.shortingKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length || typeof inputValue === 'number') {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'ALL' || inputValue === '')) {
      const rm = this.filterParams.values.find((d: any) => d.key === 'status')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }
    }
  }

  assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      case 'DROP_DOWN':
        stringDataType = col?.shortingKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }
  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }
  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
      this.getAll();
    });
  }

  setSearchEndDate(event: any, col: any) {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.key,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.creationDate = null;
    this.selectedCategoryId = undefined;
    this.makeIds = this.modelIds = this.unitTypeIds = [];
    this.makes = this.models = this.unitTypes = [];
    this.filterParams.values = Object.assign([], this.initialFilterParams);
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  getFilterSaveParams(): FilterList {
    return {
      module: FilterModuleName.CUSTOMER_LEAD.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }

  callFilterApiAgain() {
    this.getFilterDetail();
  }

  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    this.crmService.getListWithFiltersWithPagination<CustomerLeadListFilter, CustomerLeadListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.admin.crm.customerFilter)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content, downloadAll)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "customer-lead");
          this.isExporting = false;
        });
      });
  }

  allMakes(res: CustomerLeadListItem): string {
    return res.makes.map(make => make.makeName).join(', ');
  }
  allUnitModels(res: CustomerLeadListItem): string {
    return res.unitModels.map(make => make.unitModelName).join(', ');
  }

  getExcelData(customerLead: CustomerLeadListItem[], downloadAll = false) {
    const excelData = customerLead.map(res => ({
      'Lead Id': res?.id,
      'Customer': res?.contactName,
      'Creation Date': Utils.dateIntoUserReadableFormat(res?.createdDate ?? ''),
      'Lead Description': res?.leadDescription,
      'Status': res?.status,
      'Unit Type': res?.unitType?.name,
      'Minimum Year': res?.minYear,
      'Maximum Year': res?.maxYear,
      'Make': this.allMakes(res),
      'Sales Person': res?.reporter?.name,
      'Model': this.allUnitModels(res),
      'Category': res?.category?.name,
      'Matching Count': res?.matchingCount
    }))
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.defaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }

    return excelData;
  }

  getAllMake(): void {
    this.dropdownLoaders.make = true;
    this.makeModelService.get<Array<MakeModel>>('')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<MakeModel>) => {
          this.makesMasterData = res;
          this.makes = res;
          this.dropdownLoaders.make = false;
          this.cdf.detectChanges();
        }
      });
  }

  getAllModels(): void {
    this.dropdownLoaders.model = true;
    this.unitTypeService.get<Array<Model>>(`${API_URL_UTIL.admin.unitSpecification.unitModels}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<Model>) => {
          this.modelsMasterData = res;
          this.models = res;
          this.dropdownLoaders.model = false;
          this.cdf.detectChanges();
        }
      });
  }

  getAllUnitType(): void {
    this.dropdownLoaders.unitType = true
    this.unitTypeService.get<Array<UnitType>>(`${API_URL_UTIL.admin.unitSpecification.unitTypes}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<UnitType>) => {
          this.unitTypesMasterData = res;
          this.unitTypes = res;
          this.dropdownLoaders.unitType = false;
          this.cdf.detectChanges();
        }
      });
  }

  getCategoryTypes() {
    if (!this.categoryTypes?.length) {
      this.dropdownLoaders.category = true
      this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
        next: (categoryTypes: Array<IdNameModel>) => {
          this.categoryTypes = categoryTypes;
          this.dropdownLoaders.category = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  clearMakes(col: ColumnItem): void {
    this.makeIds = [];
    this.getModels();
    this.tableSearchByColumn([], col);
  }

  clearModels(col: ColumnItem): void {
    this.modelIds = [];
    this.tableSearchByColumn([], col);
  }

  clearUnitTypes(col: ColumnItem): void {
    this.unitTypeIds = [];
    this.tableSearchByColumn([], col);
  }

  onCategoryChange() {
    this.makes = this.unitTypes = this.models = [];
    this.makeIds = this.unitTypeIds = this.modelIds = [];
    this.getMake();
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
