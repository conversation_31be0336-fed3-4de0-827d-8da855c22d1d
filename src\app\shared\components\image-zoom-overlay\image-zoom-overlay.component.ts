import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faIcons } from '../../../@core/utils/font-awesome-icon.utils';

export interface ZoomImage {
  fullUrl: string;
  alt?: string;
}

@Component({
  selector: 'app-image-zoom-overlay',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './image-zoom-overlay.component.html',
  styleUrls: ['./image-zoom-overlay.component.scss'],
})
export class ImageZoomOverlayComponent implements OnInit, OnDestroy {
  @Input() images: ZoomImage[] = [];
  @Input() selectedIndex: number = 0;
  @Input() isVisible: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() indexChange = new EventEmitter<number>();

  @ViewChild('zoomImage', { static: false }) zoomImageRef!: ElementRef<HTMLImageElement>;

  // Zoom state
  zoomLevel: number = 1;
  minZoom: number = 0.5;
  maxZoom: number = 5;
  panX: number = 0;
  panY: number = 0;

  // Pan state
  isPanning: boolean = false;
  lastPanX: number = 0;
  lastPanY: number = 0;

  // Help and hints state
  showHelp: boolean = false;
  showFeatureHints: boolean = true;
  hasUserPanned: boolean = false;
  hasUserNavigated: boolean = false;
  hasUserZoomed: boolean = false;
  hintsShownCount: { [key: string]: number } = {};
  maxHintShowCount: number = 2;
  hintTimeout: any;
  hintDisplayTimeout: any;

  // Hint visibility flags
  showZoomHintFlag: boolean = false;
  showPanHintFlag: boolean = false;
  showNavigateHintFlag: boolean = false;

  faIcons = faIcons;
  Math = Math;

  ngOnInit(): void {
    if (this.isVisible) {
      document.body.style.overflow = 'hidden';
      this.initializeHints();
    }
  }

  ngOnChanges(changes: any): void {
    if (changes.isVisible && changes.isVisible.currentValue) {
      this.initializeHints();
    }
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isVisible) return;

    switch (event.key) {
      case 'Escape':
        this.closeOverlay();
        break;
      case 'ArrowLeft':
        this.navigateImage('prev');
        break;
      case 'ArrowRight':
        this.navigateImage('next');
        break;
      case '+':
      case '=':
        this.zoomIn();
        break;
      case '-':
        this.zoomOut();
        break;
      case '0':
        this.resetZoom();
        break;
      case '?':
      case 'h':
      case 'H':
        this.toggleHelp();
        break;
    }
  }

  closeOverlay(): void {
    this.isVisible = false;
    this.resetZoomState();
    document.body.style.overflow = 'auto';
    this.close.emit();
  }

  navigateImage(direction: 'prev' | 'next'): void {
    if (direction === 'prev' && this.selectedIndex > 0) {
      this.selectedIndex--;
      this.handleUserNavigationAction();
    } else if (direction === 'next' && this.selectedIndex < this.images.length - 1) {
      this.selectedIndex++;
      this.handleUserNavigationAction();
    }
    this.indexChange.emit(this.selectedIndex);
    this.resetZoomState();
  }

  getCurrentImage(): ZoomImage | undefined {
    return this.images[this.selectedIndex];
  }

  // Zoom Control Methods
  zoomIn(): void {
    if (this.zoomLevel < this.maxZoom) {
      this.zoomLevel = Math.min(this.zoomLevel * 1.2, this.maxZoom);
      this.handleUserZoomAction();
    }
  }

  zoomOut(): void {
    if (this.zoomLevel > this.minZoom) {
      this.zoomLevel = Math.max(this.zoomLevel / 1.2, this.minZoom);
      if (this.zoomLevel <= 1) {
        this.panX = 0;
        this.panY = 0;
      }
      this.handleUserZoomAction();
    }
  }

  resetZoom(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
    this.handleUserZoomAction();
  }

  onWheel(event: WheelEvent): void {
    event.preventDefault();
    if (event.deltaY < 0) {
      this.zoomIn();
    } else {
      this.zoomOut();
    }
  }

  // Pan Methods
  startPan(event: MouseEvent): void {
    if (this.zoomLevel <= 1) return;

    this.isPanning = true;
    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
    event.preventDefault();
  }

  onPan(event: MouseEvent): void {
    if (!this.isPanning || this.zoomLevel <= 1) return;

    const deltaX = event.clientX - this.lastPanX;
    const deltaY = event.clientY - this.lastPanY;

    this.panX += deltaX;
    this.panY += deltaY;

    if (!this.hasUserPanned) {
      this.handleUserPanAction();
    }

    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
  }

  endPan(): void {
    this.isPanning = false;
  }

  // Helper Methods
  getImageTransform(): string {
    return `scale(${this.zoomLevel}) translate(${this.panX / this.zoomLevel}px, ${this.panY / this.zoomLevel}px)`;
  }

  onImageLoad(): void {
    // Reset zoom state when new image loads
    this.resetZoomState();
  }

  private resetZoomState(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
    this.isPanning = false;
    this.hasUserPanned = false;
    this.updateHintVisibility();
  }

  // Help and tooltip methods
  toggleHelp(): void {
    this.showHelp = !this.showHelp;
  }

  getImageContainerTooltip(): string {
    if (this.zoomLevel > 1) {
      return 'Click and drag to pan around the image. Use mouse wheel to zoom.';
    }
    return 'Use mouse wheel to zoom in/out. Click zoom buttons for controls.';
  }

  // Initialize hints when overlay opens
  private initializeHints(): void {
    if (!this.shouldShowHints()) {
      return;
    }

    this.showFeatureHints = true;
    this.hasUserPanned = false;
    this.hasUserNavigated = false;
    this.hasUserZoomed = false;
    this.resetHintFlags();
    this.updateHintVisibility();
  }

  // Check if we should show hints based on usage count
  private shouldShowHints(): boolean {
    const totalHintsShown = Object.values(this.hintsShownCount).reduce((sum, count) => sum + count, 0);
    return totalHintsShown < 6;
  }

  // Reset all hint flags
  private resetHintFlags(): void {
    this.showZoomHintFlag = false;
    this.showPanHintFlag = false;
    this.showNavigateHintFlag = false;
  }

  // Update hint visibility based on current state
  private updateHintVisibility(): void {
    if (!this.showFeatureHints) {
      this.resetHintFlags();
      return;
    }

    this.checkAndShowZoomHint();
    this.checkAndShowPanHint();
    this.checkAndShowNavigateHint();
  }

  // Check and show zoom hint
  private checkAndShowZoomHint(): void {
    const shouldShow = this.zoomLevel <= 1 && !this.hasUserZoomed && this.canShowHint('zoom');
    if (shouldShow && !this.showZoomHintFlag) {
      this.showZoomHintFlag = true;
      this.markHintAsShown('zoom');
      this.scheduleHintHide();
    }
  }

  // Check and show pan hint
  private checkAndShowPanHint(): void {
    const shouldShow = this.zoomLevel > 1 && !this.hasUserPanned && this.canShowHint('pan');
    if (shouldShow && !this.showPanHintFlag) {
      this.showPanHintFlag = true;
      this.markHintAsShown('pan');
      this.scheduleHintHide();
    }
  }

  // Check and show navigate hint
  private checkAndShowNavigateHint(): void {
    const shouldShow = this.images.length > 1 && !this.hasUserNavigated && this.canShowHint('navigate');
    if (shouldShow && !this.showNavigateHintFlag) {
      this.showNavigateHintFlag = true;
      this.markHintAsShown('navigate');
      this.scheduleHintHide();
    }
  }

  // Check if specific hint can be shown
  private canShowHint(hintType: string): boolean {
    const count = this.hintsShownCount[hintType] || 0;
    return count < this.maxHintShowCount;
  }

  // Mark hint as shown and increment counter
  private markHintAsShown(hintType: string): void {
    const count = this.hintsShownCount[hintType] || 0;
    this.hintsShownCount[hintType] = count + 1;
  }

  // Schedule hint to hide after 10 seconds
  private scheduleHintHide(): void {
    if (this.hintDisplayTimeout) {
      clearTimeout(this.hintDisplayTimeout);
    }
    this.hintDisplayTimeout = setTimeout(() => {
      this.resetHintFlags();
    }, 10000);
  }

  // Handle user zoom actions
  private handleUserZoomAction(): void {
    if (!this.hasUserZoomed) {
      this.hasUserZoomed = true;
      this.hideHintsAfterUserAction();
    }
    this.updateHintVisibility();
  }

  // Handle user pan actions
  private handleUserPanAction(): void {
    this.hasUserPanned = true;
    this.hideHintsAfterUserAction();
  }

  // Handle user navigation actions
  private handleUserNavigationAction(): void {
    this.hasUserNavigated = true;
    this.hideHintsAfterUserAction();
  }

  // Hide hints immediately after user performs action
  private hideHintsAfterUserAction(): void {
    this.resetHintFlags();
    if (this.hintDisplayTimeout) {
      clearTimeout(this.hintDisplayTimeout);
    }
  }

  ngOnDestroy(): void {
    document.body.style.overflow = 'auto';
    if (this.hintTimeout) {
      clearTimeout(this.hintTimeout);
    }
    if (this.hintDisplayTimeout) {
      clearTimeout(this.hintDisplayTimeout);
    }
  }
}
