import { NgModule } from "@angular/core";
import { PreventClearFilterDirective } from './clear-filter-input.directive';
import { ClickOutsideDirective } from "./click-outside.directive";
import { CommingSoonDirective } from "./coming-soon.directive";
import { EmailValidationDirective } from './email-validation.directive';
import { GroupByPipe } from "./group-by.directive";
import { HasPermissionDirective } from './has-permission.directive';
import { ImageIconDirective } from "./image-icon.directive";
import { MentionPositionDirective } from './mention-position.directive';
import { NameValidationDirective } from './name-validation.directive';
import { NumberOnlyDirective } from './number-only.directive';
import { PasswordEyeDirective } from './password-eye.directive';
import { PasswordValidationDirective } from './password-validation.directive';
import { PipelineStatusDirective } from "./pipeline-status.directive";
import { ShowLoaderOnApiCallDirective } from "./show-loader-on-apicall.directive";
import { SortByDirective } from "./sort-by.directive";
import { SortDirective } from "./sort.directive";
import { ToggleMoreLinksButtonDirective } from "./toggle-more-links-button.directive";

const DIRECTIVES = [
  ClickOutsideDirective,
  ShowLoaderOnApiCallDirective,
  PasswordEyeDirective,
  ImageIconDirective,
  SortDirective,
  SortByDirective,
  GroupByPipe,
  PipelineStatusDirective,
  CommingSoonDirective,
  HasPermissionDirective,
  MentionPositionDirective,
  ToggleMoreLinksButtonDirective,
  PasswordValidationDirective,
  NameValidationDirective,
  EmailValidationDirective,
  PreventClearFilterDirective,
  NumberOnlyDirective
]

@NgModule({
  declarations: [...DIRECTIVES],
  exports: [...DIRECTIVES]
})
export class DirectivesModule { }
