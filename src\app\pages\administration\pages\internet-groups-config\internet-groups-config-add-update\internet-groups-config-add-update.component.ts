import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { takeUntil } from 'rxjs';
import { InternetGroupsConfigService } from '../internet-groups-config.service';
import { InternetGroup } from '../models/internet-groups-config.model';

@Component({
  selector: 'app-internet-groups-config-add-update',
  templateUrl: './internet-groups-config-add-update.component.html',
})
export class InternetGroupsConfigAddUpdateComponent extends BaseComponent implements OnInit {
  @Input() selectedInternetGroup: InternetGroup | null = null;
  @Output() onClose = new EventEmitter<boolean>();

  internetGroupForm!: FormGroup;
  title!: string;
  isEditMode = false;
  redirectUrl!: string;

  constructor(private readonly fb: FormBuilder, private readonly internetGroupsConfigService: InternetGroupsConfigService, private readonly toasterService: AppToasterService, private readonly commonSharedService: CommonSharedService, private readonly activeRoute: ActivatedRoute, private readonly router: Router) {
    super();
  }

  ngOnInit(): void {
    if (this.selectedInternetGroup?.id) {
      this.isEditMode = true;
    }
    this.title = this.isEditMode ? 'Edit Shadow Group' : 'Add Shadow Group';
    this.initializeFormGroup();
    this.activeRoute.queryParams.subscribe(params => {
      if (params?.returnUrl) {
        this.redirectUrl = params?.returnUrl;
      }
    });
    this.commonSharedService.setBlockUI$(false);
  }

  initializeFormGroup(): void {
    this.internetGroupForm = this.fb.group({
      id: [this.isEditMode ? this.selectedInternetGroup?.id : null],
      name: [this.isEditMode ? this.selectedInternetGroup?.name : null, [Validators.required]],
    });
  }

  onCancel(): void {
    this.onClose.emit(false);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  onSubmit(): void {
    if (this.internetGroupForm.invalid) {
      this.internetGroupForm.markAllAsTouched();
      return;
    }
    this.isEditMode ? this.onUpdate() : this.onAdd();
  }

  onAdd(): void {
    this.internetGroupsConfigService
      .add(this.internetGroupForm.value)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', 'Shadow Group'));
          this.goBack();
          this.onClose.emit(true);
        },
      });
  }

  onUpdate(): void {
    this.internetGroupsConfigService
      .update(this.internetGroupForm.value)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Shadow Group'));
          this.goBack();
          this.onClose.emit(true);
        },
      });
  }
}
