<div class="inventory-general-div">
  <form (ngSubmit)="onSubmit()">
    <section [formGroup]="generalInfoFormGroup">
      <p-accordion class="nested-accordion" [multiple]="true">
        <div class="row">
          <div class="col-12">
            <p-accordionTab [(selected)]="accordionTabs.generalInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.generalInfo }">
                  <span>General Information</span>
                  <em class="pi" [ngClass]="accordionTabs.generalInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="generalInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <!-- NOTE: The following modules are currently disabled per client's request. -->
          <!-- <div class="col-12" *ngIf="isEditMode && pipelineDetails?.phases">
            <p-accordionTab [(selected)]="accordionTabs.pipelineProgress">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.pipelineProgress }">
                  <span>Pipeline Progress</span>
                  <em class="pi" [ngClass]="accordionTabs.pipelineProgress ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="pipelineTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div> -->
          <div class="col-md-6 col-12">
            <p-accordionTab [(selected)]="accordionTabs.previousOwner">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.previousOwner }">
                  <span>Previous Owner</span>
                  <em class="pi" [ngClass]="accordionTabs.previousOwner ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="previousOwnerTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6 col-12">
            <p-accordionTab [(selected)]="accordionTabs.odometerReadings">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.odometerReadings }">
                  <span>Odometer Readings</span>
                  <em class="pi" [ngClass]="accordionTabs.odometerReadings ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="odometerReadingTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6 col-12">
            <p-accordionTab [(selected)]="accordionTabs.lotLocation">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.lotLocation }">
                  <span>Lot Location</span>
                  <em class="pi" [ngClass]="accordionTabs.lotLocation ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="lotLocationTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6 col-12">
            <p-accordionTab [(selected)]="accordionTabs.internetOptions">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.internetOptions }">
                  <span>Internet Options</span>
                  <em class="pi" [ngClass]="accordionTabs.internetOptions ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="internetOptionsTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
        </div>
      </p-accordion>
    </section>
  </form>

  <!-- NOTE: The following modules are currently disabled per client's request. -->
  <!-- <ng-template #pipelineTemplate>
    <div class="inventory-general-pipeline row">
      <p-timeline [value]="pipelineDetails.phases" layout="horizontal">
        <ng-template pTemplate="marker" let-pipeline>
          <span [class]="pipeline?.taskStatus" appPipelineStatus>{{ pipeline?.sequenceNumber }}</span>
        </ng-template>
        <ng-template pTemplate="content" let-pipeline>
          <span class="text-pre-wrap">{{ pipeline?.shop?.name }}</span
          ><span class="initials" pTooltip="{{ pipeline?.assignee?.firstName }} {{ pipeline?.assignee?.lastName }}" tooltipPosition="bottom">{{
            utils.getInitials(pipeline?.assignee?.firstName, pipeline?.assignee?.lastName) | uppercase
          }}</span>
        </ng-template>
      </p-timeline>
      <button class="btn btn-primary left view-pipeline" (click)="showPipelineDetails()" type="button"><span>View Pipeline</span></button>
    </div>
  </ng-template> -->

  <ng-template #generalInfoTemplate [formGroup]="generalInfoFormGroup">
    <div class="row" formGroupName="generalInformation">
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Category</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="categoryTypes"
          formControlName="unitTypeCategoryId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select category type"
          (onChange)="changeCategory($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.categoryType, data: categoryTypes }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.unitTypeCategoryId"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Stock#</label>
        <input class="form-control" type="text" placeholder="Enter stock number" formControlName="stockNumber" />
        <app-error-messages [control]="generalInformation.controls.stockNumber"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label>VIN#</label>
        <input class="form-control" type="text" placeholder="Enter VIN number" formControlName="vin" />
        <app-error-messages [control]="generalInformation.controls.vin"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Status</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="inventoryStatuses"
          optionDisabled="isDisabled"
          formControlName="unitStatusId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select status"
          (onChange)="changeStatus($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.status, data: inventoryStatuses }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.unitStatusId"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Unit Type</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_UNIT_TYPE]">
          <button
            class="btn btn-primary add-btn"
            id="addUnitTypeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('unit-type')"
          ></button>
        </ng-container>
        <p-dropdown
          appPreventClearFilter 
          appendTo="body"
          [options]="unitTypes"
          formControlName="unitTypeId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select unit type"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.unitTypes, data: unitTypes }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.unitTypeId"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Year</label>
        <input class="form-control" type="number" placeholder="Enter year" formControlName="year" />
        <app-error-messages [control]="generalInformation.controls.year"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Make</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
          <button
            class="btn btn-primary add-btn"
            id="addMakeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('make')"
          ></button>
        </ng-container>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="makes"
          formControlName="makeId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select make"
          (onChange)="changeMake($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.makes, data: makes }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.makeId"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Model</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_MAKE_MODEL]">
          <button
            class="btn btn-primary add-btn"
            id="addModelBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openModel('model')"
          ></button>
        </ng-container>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="models"
          formControlName="unitModelId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select model"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.models, data: models }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.unitModelId"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Owned By</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="dealerOptions"
          formControlName="ownerId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select owner"
          (onChange)="setLocation($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.ownedBy, data: dealerOptions }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.ownerId"></app-error-messages>
      </div>
      <div class="col-lg-3 col-md-6 col-12">
        <label class="required">Designation</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="designations"
          formControlName="designationId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select designation"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.designations, data: designations }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="generalInformation.controls.designationId"></app-error-messages>
      </div>
    </div>
  </ng-template>

  <ng-template #previousOwnerTemplate [formGroup]="previousOwnerFormGroup">
    <div class="row">
      <div class="col-lg-6 col-md-8 col-12" formGroupName="previousOwner">
        <label>Select Contact/Vendor/Supplier</label>
        <ng-container *ngIf="!isViewMode">
          <button
            class="btn btn-primary add-btn"
            id="addVendorBtn"
            type="button"
            *appHasPermission="[permissionActions.CREATE_VENDORS]"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="showDialog()"
          ></button>
        </ng-container>
        <p-dialog
          header="You can create either Vendor or Supplier or Contact from here."
          [(visible)]="displaySelectionDialog"
          [modal]="true"
          [style]="{ width: '45vw' }"
          [draggable]="false"
          [resizable]="false"
        >
          <ng-template pTemplate="footer">
            <p-button label="Vendor" styleClass="p-button-text" (click)="openModel(ModelType.VENDOR)"></p-button>
            <p-button label="Supplier" styleClass="p-button-text" (click)="openModel(ModelType.SUPPLIER)"></p-button>
            <p-button label="Contact" styleClass="p-button-text" (click)="openModel(ModelType.CONTACT)"></p-button>
          </ng-template>
        </p-dialog>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="vendors"
          formControlName="previousOwnerContactId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select"
          [virtualScroll]="true"
          [itemSize]="30"
          (onChange)="displayContactDetails($event)"
          [optionDisabled]="isOptionDisabled.bind(this)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: unitTypes }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span [ngClass]="{'disabled-dropdown-item': item?.archived}">
              {{ item.name }}
              <span *ngIf="item.archived">{{ constants.archived }}</span>
            </span>
          </ng-template>
        </p-dropdown>
      </div>
      <div class="col-lg-6 col-md-8 col-12" *ngIf="displayContact">
        <p-card>
          <p class="bold">
            {{ displayContact.contactName }}
            <button
              class=""
              id="addMakeBtn"
              type="button"
              class="displayContactIcons"
              [appImageIconSrc]="constants.staticImages.icons.map"
              (click)="toggleGoogleMapPopUp(displayContact)"
              *ngIf="displayContact?.streetAddress || displayContact?.zipcode || displayContact?.city || displayContact?.state"
            ></button>
            <button class="displayContactIcons" id="addMakeBtn" type="button" [appImageIconSrc]="constants.staticImages.icons.contact"></button>
          </p>
          <p class="displayContactDetails company">{{ displayContact.company }}</p>
          <p class="displayContactDetails">{{ displayContact.phoneWork }}</p>
          <p class="displayContactDetails">{{ displayContact.email }}</p>
        </p-card>
        <p-sidebar
          [closeOnEscape]="false"
          [dismissible]="false"
          [(visible)]="showGoogleMapSideBar"
          position="right"
          (onHide)="showGoogleMapSideBar = false"
          [blockScroll]="true"
          [showCloseIcon]="false"
          styleClass="p-sidebar-md"
          [baseZIndex]="10000"
          appendTo="body"
        >
          <app-google-map
            (onClose)="toggleGoogleMapPopUp(displayContact)"
            *ngIf="showGoogleMapSideBar"
            [addressGroup]="displayContact?.streetAddress"
            [address]="fullAddress"
          ></app-google-map>
        </p-sidebar>
      </div>
    </div>
  </ng-template>

  <ng-template #odometerReadingTemplate>
    <div [formGroup]="unitOfDistanceFormGroup">
      <div class="col-12 d-flex" formGroupName="unitOfDistance">
        <div class="p-field-radiobutton">
          <p-radioButton name="id" value="1" formControlName="id" inputId="mi" [(ngModel)]="unitOfDistance"> </p-radioButton>
          <label for="mi">mi</label>
        </div>
        <div class="p-field-radiobutton">
          <p-radioButton name="id" value="2" formControlName="id" inputId="km" [(ngModel)]="unitOfDistance"> </p-radioButton>
          <label for="km">km</label>
        </div>
      </div>
    </div>
    <div [formGroup]="odometerReadingFormGroup">
      <div formGroupName="odometer">
        <div class="row mt-1">
          <div class="col-lg-4 col-md-6 col-12">
            <label>Odometer</label>
            <input class="form-control" type="number" placeholder="Enter odometer reading" formControlName="odometerReading" appNumberOnly/>
          </div>
          <div class="col-lg-4 col-md-6 col-12">
            <label>Hours</label>
            <input class="form-control" type="number" placeholder="Enter hours" formControlName="hours" appNumberOnly/>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #lotLocationTemplate [formGroup]="lotLocationFormGroup">
    <div class="row mt-1" formGroupName="unitLotLocation">
      <div class="col-lg-6 col-md-8 col-12">
        <label class="required">Receiving Location</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="dealerOptions"
          formControlName="receivingLocationId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select receiving location"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.lotLocation, data: dealerOptions }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="unitLotLocation.controls.receivingLocationId"></app-error-messages>
      </div>
      <div class="col-lg-6 col-md-8 col-12">
        <label class="required">Current Location</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="dealerOptions"
          formControlName="currentLocationId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          placeholder="Select current location"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.currentLocation, data: dealerOptions }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="unitLotLocation.controls.currentLocationId"></app-error-messages>
      </div>
    </div>
  </ng-template>

  <ng-template #internetOptionsTemplate>
    <div class="row">
      <div class="col-12 d-flex" [formGroup]="internetOptionFormGroup">
        <div formGroupName="internetOption">
          <p-checkbox class="me-5" formControlName="displayOnWeb" label="Display on web" [binary]="true" (onChange)="updateDisplayOnWeb($event.checked)"></p-checkbox>
          <p-checkbox name="groupname" value="displayOnWeb" formControlName="priceOnWeb" label="Price on web" [binary]="true"></p-checkbox>
        </div>
      </div>
      <div class="col-12 internet-groups" [formGroup]="internetGroupForm">
        <div formGroupName="internetGroups">
          <label>Shadow Groups</label>
          <ng-container *ngIf="!isViewMode">
            <button
              class="btn btn-primary add-btn"
              id="addInternetGroupBtn"
              type="button"
              *appHasPermission="[permissionActions.CREATE_INVENTORY]"
              [appImageIconSrc]="constants.staticImages.icons.add"
              (click)="openModel(ModelType.INTERNET_GROUP)"
            ></button>
          </ng-container>
          <p-multiSelect
            appPreventClearFilter
            appendTo="body"
            [options]="internetGroupOptions"
            optionLabel="name"
            optionValue="id"
            formControlName="id"
            filterBy="name"
            placeholder="Select shadow groups"
            [(ngModel)]="internetGroup"
            name="shadow group selection {{ internetGroup }}"
            selectedItemsLabel="{0} shadow group selected"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.internetGroup, data: internetGroupOptions }"></ng-container>
            </ng-template>
          </p-multiSelect>
        </div>
      </div>
      <div class="col-12 internet-groups" [formGroup]="generalInfoFormGroup">
        <div formGroupName="generalInformation">
          <label class="required">Advertising Banner</label>
          <ng-container *ngIf="!isViewMode">
            <button
              class="btn btn-primary add-btn"
              id="addVendorBtn"
              type="button"
              *appHasPermission="[permissionActions.CREATE_ADVERTISE]"
              [appImageIconSrc]="constants.staticImages.icons.add"
              (click)="openModel(ModelType.ADVERTISING_CONFIG)"
            ></button>
          </ng-container>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="banners"
            formControlName="advertisingId"
            optionLabel="name"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="name"
            placeholder="Select current location"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.advertising, data: banners }"></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.name }}</span>
            </ng-template>
          </p-dropdown>
          <app-error-messages [control]="generalInformation.controls.advertisingId"></app-error-messages>
        </div>
      </div>
      <div class="col-12 public-inventory-wrapper" *ngIf="inventoryInfo?.id && internetOptionFormGroup?.value.internetOption.displayOnWeb">
        <div>
          <a
            target="_blank"
            [href]="
              environment?.forntendUrl +
              'public-inventory/' +
              inventoryDetails?.generalInformation?.owner?.abbreviation +
              '?id=' +
              inventoryInfo?.id +
              '&abbreviation=' +
              inventoryDetails?.generalInformation?.owner?.abbreviation
            "
          >
            Public Inventory Detail
          </a>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #emptyMessage let-loader="loader" let-data="data">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </ng-template>
</div>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateMake"
  position="right"
  (onHide)="modelPopups.showCreateMake = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-make (onClose)="onAddEditPopupClose('make')" *ngIf="modelPopups.showCreateMake" [categoryId]="categoryId"> </app-add-new-make>
</p-sidebar>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateModel"
  position="right"
  (onHide)="modelPopups.showCreateModel = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-model (onClose)="onAddEditPopupClose('model')" *ngIf="modelPopups.showCreateModel" [makeId]="selectedMakeId"> </app-add-new-model>
</p-sidebar>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateVendor"
  position="right"
  (onHide)="modelPopups.showCreateVendor = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="width-80"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-vendor-add (onClose)="onAddEditPopupClose('vendor')" *ngIf="modelPopups.showCreateVendor" [vendorInfo]="selectedVendor"> </app-vendor-add>
</p-sidebar>

<p-sidebar
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateContact"
  position="right"
  (onHide)="modelPopups.showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="width-80"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-add (onClose)="onAddEditPopupClose('contact')" *ngIf="modelPopups.showCreateContact"> </app-crm-contact-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateSupplier"
  position="right"
  (onHide)="modelPopups.showCreateSupplier = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-supplier-add (onClose)="onAddEditPopupClose(ModelType.SUPPLIER)" *ngIf="modelPopups.showCreateSupplier"> </app-supplier-add>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showAdvertisingBanner"
  position="right"
  (onHide)="modelPopups.showAdvertisingBanner = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-advertising-add-edit (onClose)="onAddEditPopupClose(ModelType.ADVERTISING_CONFIG, $event)" *ngIf="modelPopups.showAdvertisingBanner"> </app-advertising-add-edit>
</p-sidebar>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showInternetGroup"
  position="right"
  (onHide)="modelPopups.showInternetGroup = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-internet-groups-config-add-update (onClose)="onAddEditPopupClose(ModelType.INTERNET_GROUP, $event)" *ngIf="modelPopups.showInternetGroup"> </app-internet-groups-config-add-update>
</p-sidebar>

<!-- NOTE: The following modules are currently disabled per client's request. -->
<!-- <p-sidebar
  [(visible)]="modelPopups.showSoldPipelineDetails"
  [fullScreen]="true"
  (onHide)="modelPopups.showSoldPipelineDetails = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  [baseZIndex]="10000"
  appendTo="body"
  *ngIf="selectedTruckBoard"
>
  <app-sold-truck-board-add (onClose)="modelPopups.showSoldPipelineDetails = false" [soldTruckInfo]="selectedTruckBoard"></app-sold-truck-board-add>
</p-sidebar> -->

<!-- NOTE: The following modules are currently disabled per client's request. -->
<!-- <p-sidebar
  styleClass="pipeline-config"
  [(visible)]="modelPopups.showStockPipelineDetails"
  [fullScreen]="true"
  (onHide)="modelPopups.showStockPipelineDetails = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  [baseZIndex]="10000"
  appendTo="body"
  *ngIf="selectedTruckBoard"
>
  <app-stock-truck-board-add (onClose)="modelPopups.showStockPipelineDetails = false" [stockTruckInfo]="selectedTruckBoard"> </app-stock-truck-board-add>
</p-sidebar> -->

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="modelPopups.showCreateUnitType"
  position="right"
  (onHide)="modelPopups.showCreateUnitType = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-add-new-unit-type (onClose)="onAddEditPopupClose('unit-type')" [unitTypeCategoryId]="categoryId" *ngIf="modelPopups.showCreateUnitType"> </app-add-new-unit-type>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="modelPopups.showInventoryToBeSoldModal"
  (onHide)="modelPopups.showInventoryToBeSoldModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-inventory-sold
    [inventoryInfo]="selectedInventoryInfo"
    [selectedStatus]="2"
    [isGeneralInfo]="true"
    (closeModal)="onSoldModalClose($event)"
    (soldInventoriesId)="setSoldInventoriesId($event)"
  >
  </app-inventory-sold>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="modelPopups.showInventoryToBeHoldModal"
  (onHide)="modelPopups.showInventoryToBeHoldModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-inventory-hold
    [inventoryInfo]="selectedInventoryInfo"
    [selectedStatus]="12"
    [isGeneralInfo]="true"
    (closeModal)="onHoldModalClose($event)"
    (holdInventoriesId)="setHoldInventoriesId($event)"
  >
  </app-inventory-hold>
</p-sidebar>
