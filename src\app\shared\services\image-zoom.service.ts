import { Injectable } from '@angular/core';
import { ZoomImage } from '../components/image-zoom-overlay/image-zoom-overlay.component';

@Injectable({
  providedIn: 'root'
})
export class ImageZoomService {

  prepareZoomImages<T>(
    items: T[], 
    urlProperty: keyof T, 
    altPrefix = 'Image'
  ): ZoomImage[] {
    return items.map((item, index) => ({
      fullUrl: item[urlProperty] as string,
      alt: `${altPrefix} ${index + 1}`
    }));
  }

  prepareTaskAttachmentZoomImages(attachments: any[]): ZoomImage[] {
    return attachments.map(attachment => ({
      fullUrl: attachment.fullTaskAttachmentUrl,
      alt: `Task Attachment ${attachment.id}`
    }));
  }

  prepareInventoryPhotoZoomImages(photos: any[]): ZoomImage[] {
    return photos.map(photo => ({
      fullUrl: photo.fileUrl,
      alt: `Inventory Photo ${photo.id}`
    }));
  }

  prepareUnitImageZoomImages(images: any[]): ZoomImage[] {
    return images.map((image, index) => ({
      fullUrl: image.fileUrl,
      alt: `Condition Image ${index + 1}`
    }));
  }

  findImageIndex<T>(
    items: T[], 
    clickedUrl: string, 
    urlProperty: keyof T
  ): number {
    const index = items.findIndex(item => item[urlProperty] === clickedUrl);
    return Math.max(0, index);
  }

  findTaskAttachmentIndex(attachments: any[], clickedUrl: string): number {
    const index = attachments.findIndex(attachment => attachment.fullTaskAttachmentUrl === clickedUrl);
    return Math.max(0, index);
  }

  createZoomConfig(images: ZoomImage[], selectedIndex: number, isVisible: boolean) {
    return {
      images,
      selectedIndex,
      isVisible
    };
  }

  combinePhotoArrays<T>(...photoArrays: T[][]): T[] {
    return photoArrays.reduce((combined, array) => [...combined, ...array], []);
  }

  combineTaskAttachmentsWithUploads(existingAttachments: any[], fileUploadProgresses: any[]): any[] {
    const newlyUploadedFiles = fileUploadProgresses
      .filter(progress => progress.fileProperty?.fileUrl || progress.uploadedFileUrl)
      .map(progress => ({
        fullTaskAttachmentUrl: progress.fileProperty?.fileUrl || progress.uploadedFileUrl,
        id: `temp_${progress.index}`,
        url: progress.uploadedFileUrl
      }));

    return [...existingAttachments, ...newlyUploadedFiles];
  }

  findCombinedAttachmentIndex(allAttachments: any[], clickedUrl: string): number {
    const index = allAttachments.findIndex(attachment =>
      attachment.fullTaskAttachmentUrl === clickedUrl ||
      attachment.url === clickedUrl
    );
    return Math.max(0, index);
  }

  prepareCombinedAttachmentZoomImages(allAttachments: any[]): ZoomImage[] {
    return allAttachments.map(attachment => ({
      fullUrl: attachment.fullTaskAttachmentUrl || attachment.url,
      alt: `Task Attachment ${attachment.id}`
    }));
  }
}
