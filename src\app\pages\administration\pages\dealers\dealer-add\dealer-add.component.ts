import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Constants, MESSAGES } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { DealerCreateParam, DealerListItem } from '@pages/administration/models';
// import { Address } from 'ngx-google-places-autocomplete/objects/address';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import * as saveAs from 'file-saver';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress } from 'src/app/@shared/models/file-upload.model';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';
import { DealerService } from '../dealers.service';

@Component({
  selector: 'app-dealer-add',
  templateUrl: './dealer-add.component.html',
  styleUrls: ['./dealer-add.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class DealerAddComponent extends BaseComponent implements OnInit, OnChanges {

  title = 'Add Dealer';
  dealerFormGroup!: FormGroup;
  hasDataBeenModified = false;
  isEditMode = false;
  showGoogleMapSideBar = false;
  isArchived = false;
  redirectUrl!: string;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  isFormDisabled = false;
  fileUploadProgresses: FileUploadProgress[] = [];
  currentUser!: Account | null;
  zoomImages: any[] = [];
  selectedPhotoIndex = 0;
  isShowCarousel = false;
  @Input() dealerInfo!: DealerListItem | null;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  contactNoFormat = Constants.phoneNumberMask;
  constructor(
    private readonly fb: FormBuilder,
    private readonly cdf: ChangeDetectorRef,
    private readonly dealerService: DealerService,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService,
    private readonly fileUploadService: FileUploadService,
    private readonly authService: AuthService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    this.setDealerInfoInFormGroup();
    this.getCurrentUser();
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
    this.commonSharedService.setBlockUI$(false);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // TODO Will be remove once completed
    // if (changes.dealerInfo?.currentValue) {
    //   this.title = 'Edit Dealer';
    //   this.isEditMode = true;
    // }
    if (this.dealerInfo) {
    if (this.dealerInfo?.archived) {
      this.title = 'View Dealer';
      this.isEditMode = false;
    } else {
      this.title = 'Edit Dealer';
      this.isEditMode = true;
      }
    }
  }

  private initializeFormGroup(): void {
    this.dealerFormGroup = this.fb.group({
      name: new FormControl('', [Validators.required]),
      email: new FormControl('', [Validators.email]),
      phoneNumber: new FormControl('', [Validators.required]),
      abbreviation: new FormControl('', [Validators.required]),
      contactPerson: this.newContactPersonFormGroup,
      address: this.newAddressFormGroup,
      fundingEmails: this.fb.array([]),
      logoUrl: new FormControl('')
    });
  }

  get newEmailFormGroup(): FormGroup {
    return this.fb.group({
      id: new FormControl(null),
      email: new FormControl(null, [Validators.required, Validators.email]),
      dealerId: new FormControl(null)
    });
  }

  get emailFormArray(): FormArray {
    return this.dealerFormGroup.get('fundingEmails') as FormArray;
  }

  getEmailFormGroup(index: number): FormGroup {
    return this.emailFormArray.at(index) as FormGroup;
  }

  onDeleteShop(index: number): void {
    if (!this.isFormDisabled) {
      this.emailFormArray.removeAt(index);
      this.cdf.detectChanges();
    }
  }

  onAddNewShop(): void {
    const formGroup = this.newEmailFormGroup;
    this.emailFormArray.push(formGroup);
    document.getElementById('addEmailBtn')?.scrollIntoView({
      behavior: 'smooth',
    });
    this.cdf.detectChanges();
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      this.currentUser = user;
    });
  }

  get newContactPersonFormGroup(): FormGroup {
    return this.fb.group({
      firstName: new FormControl('', [Validators.required]),
      lastName: new FormControl('', Validators.required),
      email: new FormControl('', [Validators.email]),
      phoneNumber: new FormControl('', [Validators.required])
    })
  }

  get newAddressFormGroup(): FormGroup {
    return this.fb.group({
      streetAddress: new FormControl('', [Validators.required]),
      city: new FormControl('', [Validators.required]),
      state: new FormControl('', [Validators.required]),
      zipcode: new FormControl('', [Validators.required]),
      latitude: new FormControl(''),
      longitude: new FormControl('')
    })
  }

  get addressFormGroup(): FormGroup {
    return this.dealerFormGroup.get('address') as FormGroup;
  }

  get contactPersonFormGroup(): FormGroup {
    return this.dealerFormGroup.get('contactPerson') as FormGroup;
  }

  get dealerInfoCreateParams(): DealerCreateParam {
    return {
      ...this.dealerFormGroup.value,
      organizationId: 1,
      id: this.dealerInfo?.id
    };
  }

  onSubmit(close = true): void {
    if (this.dealerFormGroup.invalid) {
      this.dealerFormGroup.markAllAsTouched();
      return;
    }
    if (this.isEditMode) {
      this.editDealer();
    } else {
      this.saveDealer(close);
    }
  }

  saveDealer(close = true): void {
    this.dealerService.add(this.dealerInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.dealerUpdateSuccess : MESSAGES.dealerAddSuccess);
      this.hasDataBeenModified = true;
      if (close) {
        this.onClose.emit(true);
      } else {
        this.dealerFormGroup.reset();
      }
    });
  }

  getSequenceNumber(index: number): number {
    return Number(index) + 1;
  }

  editDealer(): void {
    this.dealerService.update(this.dealerInfoCreateParams).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(this.isEditMode ? MESSAGES.dealerUpdateSuccess : MESSAGES.dealerAddSuccess);
      this.hasDataBeenModified = true;
      this.onClose.emit(true);
    });
  }

  onSubmitAndAddNew(): void {
    this.onSubmit(false);
  }

  onCancel(): void {
    this.onClose.emit(this.hasDataBeenModified);
    this.goBack();
  }

  goBack(): void {
    if (this.redirectUrl) {
      this.router.navigateByUrl(this.redirectUrl);
    }
  }

  private setDealerInfoInFormGroup(): void {
    if (this.dealerInfo) {
      this.dealerFormGroup.patchValue({
        name: this.dealerInfo.name,
        email: this.dealerInfo.email,
        phoneNumber: this.dealerInfo.phoneNumber,
        abbreviation: this.dealerInfo.abbreviation,
        logoUrl: this.dealerInfo.logoUrl,
        contactPerson: {
          firstName: this.dealerInfo.contactPerson.firstName,
          lastName: this.dealerInfo.contactPerson.lastName,
          email: this.dealerInfo.contactPerson.email,
          phoneNumber: this.dealerInfo.contactPerson.phoneNumber
        },
        address: {
          streetAddress: this.dealerInfo.address.streetAddress,
          city: this.dealerInfo.address.city,
          state: this.dealerInfo.address.state,
          zipcode: this.dealerInfo.address.zipcode,
          latitude: this.dealerInfo.address.latitude,
          longitude: this.dealerInfo.address.longitude,
        }
      });
      for (const fundingEmail of this.dealerInfo.fundingEmails) {
        this.emailFormArray.push(this.newEmailFormGroup);
      }

      this.emailFormArray.setValue(this.dealerInfo.fundingEmails);
    }
    this.isFormDisabled = this.isArchived = this.dealerInfo?.archived ? true : false;
    if (this.isArchived) {
      this.dealerFormGroup.disable();
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress()
  }

  handleAddressChange(address: any) {
    if (address?.address_components) {
      this.addressFormGroup.controls['streetAddress'].setValue(address.name);
      this.addressFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.addressFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':
            break;
          case 'locality':
            this.addressFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.addressFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.addressFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  getFullAddress() {
    const rowAddress = []
    const address = this.addressFormGroup.value;
    rowAddress.push(address?.streetAddress ? address.streetAddress : '')
    rowAddress.push(address?.city ? address.city : '')
    rowAddress.push(address?.state ? address.state : '')
    rowAddress.push(address?.zipcode ? address.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }

  onFileSelect(event: any): void {
    for (const file of event.target.files) {
      if (file.size > this.constants.fileSize) {
        this.toasterService.warning(MESSAGES.fileUploadMessage)
      }
      else {
        if (event.target?.files?.length) {
          const modifiedFileName = getRefactorFileName(file.name);
          const isExtensionSupported = allowExtensions(modifiedFileName, Constants.allowedImgFormats)
          if (isExtensionSupported) {
            const modifiedFile = new File([file], modifiedFileName, { type: file.type });
            this.uploadImage(modifiedFile);
          } else {
            this.toasterService.error(MESSAGES.fileTypeNotSupported);
            return
          }
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, 'Dealer', this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.fileUploadSuccess);
          this.dealerFormGroup.patchValue({
            logoUrl: fileUrl.url,
          });
          this.cdf.detectChanges();
        },
        error: () => {
          this.toasterService.error(MESSAGES.fileUploadError);
          this.fileUploadProgresses.splice(uploadProgress.index, 1);
          this.cdf.detectChanges();
        }
      });
  }

  removeFileFromUpload(fileIndex: number): void {
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl, fileIndex);
    } else {
      this.fileUploadProgresses.splice(fileIndex, 1);
      this.cdf.detectChanges();
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex?: number): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      if (fileIndex !== undefined) {
        this.fileUploadProgresses.splice(fileIndex, 1);
      } else {
        this.fileUploadProgresses = this.fileUploadProgresses.filter(progress => progress.uploadedFileUrl !== imageUrl);
      }
      this.dealerFormGroup.patchValue({
        logoUrl: null,
      });
      this.cdf.detectChanges();
    });
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');
  }

  downloadImage(): void {
    if (this.dealerInfo?.logoUrl) {
      this.fileUploadService.downloadFile(this.dealerInfo.logoUrl, this.getFileName(this.dealerInfo.logoUrl)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(this.dealerInfo.logoUrl));
        }
      });
    }
  }



  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  onViewImage(fileUrl: string | undefined): void {
    if (fileUrl) {
      this.zoomImages = [{
        fullUrl: fileUrl,
        alt: 'Dealer Logo'
      }];
      this.selectedPhotoIndex = 0;
      this.isShowCarousel = true;
    }
  }

  closeZoomOverlay(): void {
    this.isShowCarousel = false;
  }

  onImageIndexChange(index: number): void {
    this.selectedPhotoIndex = index;
  }

  deleteExistingLogo(): void {
    if (this.dealerInfo?.logoUrl) {
      this.deleteImageFromCloud(this.dealerInfo.logoUrl);
      this.dealerInfo.fullLogoUrl = undefined;
      this.dealerInfo.logoUrl = undefined;
    }
  }

}
