import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { UnitTypeCategory } from '@pages/administration/pages/specification-configration/models/specification.model';
import { InventorySpecification, InventorySpecificationResponse } from '@pages/inventory/models';
import { takeUntil } from 'rxjs';
import { SpecificationService } from '../../specification.service';

@Component({
  selector: 'app-specification-group-add-update',
  templateUrl: './specification-group-add-update.component.html',
  styleUrls: ['./specification-group-add-update.component.scss']
})
export class SpecificationGroupAddUpdateComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() categories!: Array<UnitTypeCategory>;
  @Input() selectedCategory!: number;
  @Input() groupAndFieldNames: string[] = [];
  @Input() specificationList!: InventorySpecificationResponse;
  @Input() selectedSpecificationGroup!: InventorySpecification;

  pageTitle!: string;
  specificationGroupFormGroup!: FormGroup;
  isLoading = false;
  @Output() addSpecificationGroup = new EventEmitter<InventorySpecificationResponse>();
  @Output() nameToBeUpdated = new EventEmitter<{ newName: string }>();
  @Output() closeModal = new EventEmitter<{ dataHasBeenModified: boolean, newName: string }>();

  constructor(
    private readonly specificationService: SpecificationService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.addSpecificationGroupConfiguration();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedSpecificationGroup?.currentValue) {
      this.setPageConfigurations();
    }
    if (changes.selectedCategory?.currentValue) {
      this.addSpecificationGroupConfiguration();
    }
    if (changes.specificationList?.currentValue) {
      this.specificationList = changes.specificationList.currentValue;
    }
  }

  setPageConfigurations(): void {
    if (this.selectedSpecificationGroup?.order) {
      this.pageTitle = 'Edit Specification Group';
      this.setSpecificationnGroupFormValues();
      return;
    }
    this.addSpecificationGroupConfiguration();
  }

  setSpecificationnGroupFormValues(): void {
    this.specificationGroupFormGroup.patchValue({
      name: this.selectedSpecificationGroup?.sectionName,
      multiple: this.selectedSpecificationGroup?.multiple
    });
    this.selectCategoryName();
  }

  addSpecificationGroupConfiguration(): void {
    this.selectCategoryName();
    this.pageTitle = 'Add Specification Group';
  }

  selectCategoryName(): void {
    const selectedCagtegoryName = this.categories?.find((category) => category.id === this.selectedCategory)?.name
    this.specificationGroupFormGroup.get('categoryName')?.setValue(selectedCagtegoryName);
    this.specificationGroupFormGroup.get('categoryName')?.disable();
  }

  initializeForm(): void {
    this.specificationGroupFormGroup = new FormGroup({
      name: new FormControl('', Validators.required),
      categoryName: new FormControl(null, Validators.required),
      multiple: new FormControl(false)
    });
  }

  onSubmit(): void {
    if (this.specificationGroupFormGroup.invalid) {
      this.specificationGroupFormGroup.markAllAsTouched();
      return;
    }
    if (this.groupAndFieldNames.includes(this.specificationGroupFormGroup.get('name')?.value?.toLowerCase())) {
      this.toasterService.error(MESSAGES.labelWithSameNameExists)
      return;
    }
    if (this.selectedSpecificationGroup?.order) {
      const selectedSpecificationGroupIndex = this.specificationList?.masterData?.specification?.findIndex(
        (specification) => specification.order === this.selectedSpecificationGroup?.order
      )
      this.specificationList.masterData.specification[selectedSpecificationGroupIndex].sectionName = this.specificationGroupFormGroup.get('name')?.value;
      this.specificationList.masterData.specification[selectedSpecificationGroupIndex].multiple = this.specificationGroupFormGroup.get('multiple')?.value;
    } else {
      this.specificationList.masterData.specification.push(
        this.addNewSpecificationGroupObject()
      );
    }
    this.updateSpecification();
  }

  addNewSpecificationGroupObject(): InventorySpecification {
    const lastIndexOfSpecificationList = this.specificationList.masterData.specification.length - 1;
    return {
      isShow: true,
      order: !this.specificationList?.masterData?.specification?.length ?
        1 :
        this.specificationList.masterData.specification[lastIndexOfSpecificationList].order + 1,
      sectionName: this.specificationGroupFormGroup.get('name')?.value,
      fields: [],
      id: !this.specificationList?.masterData?.specification?.length ?
        1 :
        this.specificationList.masterData.specification[lastIndexOfSpecificationList].id + 1,
      multiple: this.specificationGroupFormGroup.get('multiple')?.value
    }
  }

  updateSpecification(): void {
    this.isLoading = true;
    this.ensureFieldOrderIntegrity();
    this.specificationService.update<InventorySpecificationResponse>(this.specificationList).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: InventorySpecificationResponse) => {
        this.addSpecificationGroup.emit(res);
        this.nameToBeUpdated.emit({ newName: this.specificationGroupFormGroup.get('name')?.value });
        this.selectedSpecificationGroup?.order ?
          this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', 'Specification group')) :
          this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', 'Specification group'));
        this.onModalClose(true);
        this.isLoading = false
      }
    });
  }

  private ensureFieldOrderIntegrity(): void {
    this.specificationList.masterData.specification.forEach(section => {
      section.fields.forEach((field, index) => {
        if (field.order === undefined || field.order === null) {
          field.order = index + 1;
        }
      });
    });
  }

  onModalClose(dataHasBeenModified = false): void {
    this.closeModal.emit({ dataHasBeenModified, newName: this.specificationGroupFormGroup.get('name')?.value?.toLowerCase() ?? '' });
    this.specificationGroupFormGroup.reset();
  }
}
